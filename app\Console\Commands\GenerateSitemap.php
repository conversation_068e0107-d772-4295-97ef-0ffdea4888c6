<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use <PERSON>tie\Sitemap\Sitemap;
use Spatie\Sitemap\Tags\Url;
use App\Models\Product;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\Project;
use Carbon\Carbon;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:generate-sitemap {--force : Force regeneration even if file exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate comprehensive sitemap.xml with all dynamic content';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🗺️ Generating comprehensive sitemap...');

        $sitemap = Sitemap::create();

        // Add static pages
        $this->addStaticPages($sitemap);

        // Add dynamic content
        $this->addProducts($sitemap);
        $this->addServices($sitemap);
        $this->addBlogPosts($sitemap);
        $this->addProjects($sitemap);

        // Write sitemap to file
        $sitemapPath = public_path('sitemap.xml');
        $sitemap->writeToFile($sitemapPath);

        $this->info("✅ Sitemap generated successfully at: {$sitemapPath}");
        $this->info("📊 Total URLs: " . count($sitemap->getTags()));

        return Command::SUCCESS;
    }

    /**
     * Add static pages to sitemap
     */
    protected function addStaticPages(Sitemap $sitemap): void
    {
        $staticPages = [
            ['url' => '/', 'priority' => 1.0, 'changefreq' => 'weekly'],
            ['url' => '/about', 'priority' => 0.8, 'changefreq' => 'monthly'],
            ['url' => '/services', 'priority' => 0.9, 'changefreq' => 'weekly'],
            ['url' => '/portfolio', 'priority' => 0.8, 'changefreq' => 'weekly'],
            ['url' => '/blog', 'priority' => 0.7, 'changefreq' => 'daily'],
            ['url' => '/shop', 'priority' => 0.8, 'changefreq' => 'daily'],
            ['url' => '/contact', 'priority' => 0.6, 'changefreq' => 'monthly'],
            ['url' => '/careers', 'priority' => 0.5, 'changefreq' => 'monthly'],
        ];

        foreach ($staticPages as $page) {
            $sitemap->add(
                Url::create($page['url'])
                    ->setPriority($page['priority'])
                    ->setChangeFrequency($page['changefreq'])
                    ->setLastModificationDate(Carbon::now())
            );
        }

        $this->info("✓ Added " . count($staticPages) . " static pages");
    }

    /**
     * Add products to sitemap
     */
    protected function addProducts(Sitemap $sitemap): void
    {
        if (!class_exists(Product::class)) {
            return;
        }

        $products = Product::where('is_active', true)
            ->where('is_deleted', false)
            ->select(['slug', 'updated_at'])
            ->get();

        foreach ($products as $product) {
            $sitemap->add(
                Url::create("/shop/product/{$product->slug}")
                    ->setPriority(0.7)
                    ->setChangeFrequency('weekly')
                    ->setLastModificationDate($product->updated_at)
            );
        }

        $this->info("✓ Added {$products->count()} products");
    }

    /**
     * Add services to sitemap
     */
    protected function addServices(Sitemap $sitemap): void
    {
        if (!class_exists(Service::class)) {
            return;
        }

        $services = Service::where('is_active', true)
            ->select(['slug', 'updated_at'])
            ->get();

        foreach ($services as $service) {
            $sitemap->add(
                Url::create("/services/{$service->slug}")
                    ->setPriority(0.8)
                    ->setChangeFrequency('monthly')
                    ->setLastModificationDate($service->updated_at)
            );
        }

        $this->info("✓ Added {$services->count()} services");
    }

    /**
     * Add blog posts to sitemap
     */
    protected function addBlogPosts(Sitemap $sitemap): void
    {
        if (!class_exists(BlogPost::class)) {
            return;
        }

        $posts = BlogPost::where('is_published', true)
            ->where('published_at', '<=', now())
            ->select(['slug', 'updated_at'])
            ->get();

        foreach ($posts as $post) {
            $sitemap->add(
                Url::create("/blog/{$post->slug}")
                    ->setPriority(0.6)
                    ->setChangeFrequency('monthly')
                    ->setLastModificationDate($post->updated_at)
            );
        }

        $this->info("✓ Added {$posts->count()} blog posts");
    }

    /**
     * Add projects to sitemap
     */
    protected function addProjects(Sitemap $sitemap): void
    {
        if (!class_exists(Project::class)) {
            return;
        }

        $projects = Project::where('is_published', true)
            ->select(['slug', 'updated_at'])
            ->get();

        foreach ($projects as $project) {
            $sitemap->add(
                Url::create("/portfolio/{$project->slug}")
                    ->setPriority(0.7)
                    ->setChangeFrequency('monthly')
                    ->setLastModificationDate($project->updated_at)
            );
        }

        $this->info("✓ Added {$projects->count()} projects");
    }
}

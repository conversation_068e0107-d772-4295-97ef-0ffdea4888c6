<?php $__env->startSection('title', 'Order #' . $order->order_number); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-start">
        <div>
            <div class="flex items-center space-x-2">
                <a href="<?php echo e(route('admin.orders.index')); ?>" class="text-gray-500 hover:text-gray-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </a>
                <h1 class="text-2xl font-bold text-gray-900">Order #<?php echo e($order->order_number); ?></h1>
            </div>
            <p class="text-gray-600">Order placed on <?php echo e($order->created_at->format('F j, Y \a\t g:i A')); ?></p>
        </div>
        
        <div class="flex items-center space-x-3">
            <?php if($order->status === 'pending'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    Pending
                </span>
            <?php elseif($order->status === 'processing'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    Processing
                </span>
            <?php elseif($order->status === 'shipped'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                    Shipped
                </span>
            <?php elseif($order->status === 'delivered'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    Delivered
                </span>
            <?php elseif($order->status === 'cancelled'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    Cancelled
                </span>
            <?php endif; ?>
            
            <?php if($order->payment_status === 'pending'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                    Payment Pending
                </span>
            <?php elseif($order->payment_status === 'paid'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                    Paid
                </span>
            <?php elseif($order->payment_status === 'failed'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    Payment Failed
                </span>
            <?php elseif($order->payment_status === 'refunded'): ?>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                    Refunded
                </span>
            <?php endif; ?>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Order Items -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Order Items</h2>
                </div>
                <div class="divide-y divide-gray-200">
                    <?php $__currentLoopData = $order->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="px-6 py-4 flex items-center space-x-4">
                            <?php if($item->product && $item->product->featured_image): ?>
                                <img src="<?php echo e(Storage::url($item->product->featured_image)); ?>" 
                                     alt="<?php echo e($item->product->name); ?>" 
                                     class="w-16 h-16 object-cover rounded-lg">
                            <?php else: ?>
                                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                    <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900">
                                    <?php if($item->product): ?>
                                        <?php echo e($item->product->name); ?>

                                    <?php else: ?>
                                        <?php echo e($item->product_snapshot['name'] ?? 'Product Unavailable'); ?>

                                    <?php endif; ?>
                                </h3>
                                <?php if($item->variant): ?>
                                    <p class="text-sm text-gray-500">Variant: <?php echo e($item->variant->name); ?></p>
                                <?php elseif(isset($item->product_snapshot['variant_name'])): ?>
                                    <p class="text-sm text-gray-500">Variant: <?php echo e($item->product_snapshot['variant_name']); ?></p>
                                <?php endif; ?>

                                <?php if(isset($item->product_snapshot['sku'])): ?>
                                    <p class="text-xs text-gray-400">SKU: <?php echo e($item->product_snapshot['sku']); ?></p>
                                <?php elseif($item->product && $item->product->sku): ?>
                                    <p class="text-xs text-gray-400">SKU: <?php echo e($item->product->sku); ?></p>
                                <?php endif; ?>

                                <div class="flex items-center space-x-4 mt-1">
                                    <p class="text-sm text-gray-500">Qty: <?php echo e($item->quantity); ?></p>
                                    <p class="text-sm text-gray-500">Unit Price: <?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($item->unit_price, 2)); ?></p>
                                </div>
                            </div>
                            
                            <div class="text-right">
                                <p class="font-medium text-gray-900">
                                    <?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($item->total_price, 2)); ?>

                                </p>
                                <p class="text-xs text-gray-500">
                                    <?php echo e($item->quantity); ?> × <?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($item->unit_price, 2)); ?>

                                </p>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Order Timeline & History</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="flow-root">
                        <ul class="-mb-8">
                            <?php if($orderHistory && $orderHistory->count() > 0): ?>
                                <?php $__currentLoopData = $orderHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $historyItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <div class="relative <?php echo e($index < $orderHistory->count() - 1 ? 'pb-8' : ''); ?>">
                                            <?php if($index < $orderHistory->count() - 1): ?>
                                                <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                                            <?php endif; ?>
                                            <div class="relative flex space-x-3">
                                                <div>
                                                    <?php
                                                        $iconColor = match($historyItem->event_color) {
                                                            'green' => 'bg-green-500',
                                                            'blue' => 'bg-blue-500',
                                                            'purple' => 'bg-purple-500',
                                                            'indigo' => 'bg-indigo-500',
                                                            'red' => 'bg-red-500',
                                                            'yellow' => 'bg-yellow-500',
                                                            default => 'bg-gray-500',
                                                        };
                                                    ?>
                                                    <span class="h-8 w-8 rounded-full <?php echo e($iconColor); ?> flex items-center justify-center ring-8 ring-white">
                                                        <?php if($historyItem->event_icon === 'check-circle'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                            </svg>
                                                        <?php elseif($historyItem->event_icon === 'truck'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                                            </svg>
                                                        <?php elseif($historyItem->event_icon === 'credit-card'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                            </svg>
                                                        <?php elseif($historyItem->event_icon === 'arrow-right'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                                            </svg>
                                                        <?php elseif($historyItem->event_icon === 'x-circle'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                                            </svg>
                                                        <?php elseif($historyItem->event_icon === 'message-circle'): ?>
                                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                                            </svg>
                                                        <?php else: ?>
                                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                                            </svg>
                                                        <?php endif; ?>
                                                    </span>
                                                </div>
                                                <div class="min-w-0 flex-1 pt-1.5">
                                                    <div>
                                                        <p class="text-sm font-medium text-gray-900"><?php echo e($historyItem->description); ?></p>
                                                        <p class="text-sm text-gray-500"><?php echo e($historyItem->created_at->format('F j, Y \a\t g:i A')); ?></p>
                                                        <?php if($historyItem->user_name): ?>
                                                            <p class="text-xs text-gray-400">by <?php echo e($historyItem->user_name); ?></p>
                                                        <?php elseif($historyItem->triggered_by === 'system'): ?>
                                                            <p class="text-xs text-gray-400">System automated</p>
                                                        <?php endif; ?>
                                                        <?php if($historyItem->old_value && $historyItem->new_value): ?>
                                                            <p class="text-xs text-gray-500 mt-1">
                                                                Changed from <span class="font-medium"><?php echo e(ucfirst($historyItem->old_value)); ?></span>
                                                                to <span class="font-medium"><?php echo e(ucfirst($historyItem->new_value)); ?></span>
                                                            </p>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <!-- Fallback to basic timeline if no history exists -->
                                <li>
                                    <div class="relative pb-8">
                                        <div class="relative flex space-x-3">
                                            <div>
                                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                                    <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                                    </svg>
                                                </span>
                                            </div>
                                            <div class="min-w-0 flex-1 pt-1.5">
                                                <div>
                                                    <p class="text-sm text-gray-500">Order placed</p>
                                                    <p class="text-sm font-medium text-gray-900"><?php echo e($order->created_at->format('F j, Y \a\t g:i A')); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Customer Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Customer Information</h2>
                </div>
                <div class="px-6 py-4 space-y-4">
                    <?php if($order->user): ?>
                        <div>
                            <p class="text-sm font-medium text-gray-900"><?php echo e($order->user->first_name); ?> <?php echo e($order->user->last_name); ?></p>
                            <p class="text-sm text-gray-500"><?php echo e($order->user->email); ?></p>
                            <?php if($order->user->phone): ?>
                                <p class="text-sm text-gray-500"><?php echo e($order->user->phone); ?></p>
                            <?php endif; ?>
                            <p class="text-xs text-gray-400 mt-2">Registered Customer</p>
                        </div>
                    <?php else: ?>
                        <div>
                            <p class="text-sm font-medium text-gray-900">Guest Customer</p>
                            <p class="text-sm text-gray-500"><?php echo e($order->email); ?></p>
                            <?php if($order->phone): ?>
                                <p class="text-sm text-gray-500"><?php echo e($order->phone); ?></p>
                            <?php endif; ?>
                            <p class="text-xs text-gray-400 mt-2">Guest Checkout</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Billing Address -->
            <?php if($order->billing_address): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Billing Address</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="text-sm text-gray-900 space-y-1">
                        <?php if(!empty($order->billing_address['first_name']) || !empty($order->billing_address['last_name'])): ?>
                            <p class="font-medium"><?php echo e(trim(($order->billing_address['first_name'] ?? '') . ' ' . ($order->billing_address['last_name'] ?? ''))); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->billing_address['company'])): ?>
                            <p><?php echo e($order->billing_address['company']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->billing_address['address_line_1'])): ?>
                            <p><?php echo e($order->billing_address['address_line_1']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->billing_address['address_line_2'])): ?>
                            <p><?php echo e($order->billing_address['address_line_2']); ?></p>
                        <?php endif; ?>
                        <p>
                            <?php if(!empty($order->billing_address['city'])): ?>
                                <?php echo e($order->billing_address['city']); ?>

                            <?php endif; ?>
                            <?php if(!empty($order->billing_address['state'])): ?>
                                <?php if(!empty($order->billing_address['city'])): ?><span class="text-gray-500">, </span><?php endif; ?>
                                <?php echo e($order->billing_address['state']); ?>

                            <?php endif; ?>
                            <?php if(!empty($order->billing_address['postal_code'])): ?>
                                <?php echo e($order->billing_address['postal_code']); ?>

                            <?php endif; ?>
                        </p>
                        <?php if(!empty($order->billing_address['country'])): ?>
                            <p><?php echo e($order->billing_address['country']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->billing_address['phone'])): ?>
                            <p class="text-gray-600 mt-2"><?php echo e($order->billing_address['phone']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Shipping Address -->
            <?php if($order->shipping_address): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Shipping Address</h2>
                </div>
                <div class="px-6 py-4">
                    <div class="text-sm text-gray-900 space-y-1">
                        <?php if(!empty($order->shipping_address['first_name']) || !empty($order->shipping_address['last_name'])): ?>
                            <p class="font-medium"><?php echo e(trim(($order->shipping_address['first_name'] ?? '') . ' ' . ($order->shipping_address['last_name'] ?? ''))); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->shipping_address['company'])): ?>
                            <p><?php echo e($order->shipping_address['company']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->shipping_address['address_line_1'])): ?>
                            <p><?php echo e($order->shipping_address['address_line_1']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->shipping_address['address_line_2'])): ?>
                            <p><?php echo e($order->shipping_address['address_line_2']); ?></p>
                        <?php endif; ?>
                        <p>
                            <?php if(!empty($order->shipping_address['city'])): ?>
                                <?php echo e($order->shipping_address['city']); ?>

                            <?php endif; ?>
                            <?php if(!empty($order->shipping_address['state'])): ?>
                                <?php if(!empty($order->shipping_address['city'])): ?><span class="text-gray-500">, </span><?php endif; ?>
                                <?php echo e($order->shipping_address['state']); ?>

                            <?php endif; ?>
                            <?php if(!empty($order->shipping_address['postal_code'])): ?>
                                <?php echo e($order->shipping_address['postal_code']); ?>

                            <?php endif; ?>
                        </p>
                        <?php if(!empty($order->shipping_address['country'])): ?>
                            <p><?php echo e($order->shipping_address['country']); ?></p>
                        <?php endif; ?>
                        <?php if(!empty($order->shipping_address['phone'])): ?>
                            <p class="text-gray-600 mt-2"><?php echo e($order->shipping_address['phone']); ?></p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Payment Information -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Payment Information</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <?php if($order->payment_method): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Payment Method</span>
                            <span class="text-gray-900 capitalize"><?php echo e(str_replace('_', ' ', $order->payment_method)); ?></span>
                        </div>
                    <?php endif; ?>

                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Payment Status</span>
                        <span class="text-gray-900">
                            <?php if($order->payment_status === 'pending'): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Pending
                                </span>
                            <?php elseif($order->payment_status === 'paid'): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Paid
                                </span>
                            <?php elseif($order->payment_status === 'failed'): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Failed
                                </span>
                            <?php elseif($order->payment_status === 'refunded'): ?>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    Refunded
                                </span>
                            <?php endif; ?>
                        </span>
                    </div>

                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Currency</span>
                        <span class="text-gray-900"><?php echo e($order->currency->code ?? 'ZAR'); ?> (<?php echo e($order->currency->symbol ?? 'R'); ?>)</span>
                    </div>

                    <?php if($order->payments && $order->payments->count() > 0): ?>
                        <div class="border-t border-gray-200 pt-3">
                            <h3 class="text-sm font-medium text-gray-900 mb-2">Payment Transactions</h3>
                            <div class="space-y-2">
                                <?php $__currentLoopData = $order->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center text-xs">
                                        <div>
                                            <span class="font-mono text-gray-600"><?php echo e($payment->transaction_id); ?></span>
                                            <?php if($payment->processed_at): ?>
                                                <span class="text-gray-500">• <?php echo e($payment->processed_at->format('M j, Y g:i A')); ?></span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($payment->amount, 2)); ?></span>
                                            <?php if($payment->status === 'completed'): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    Completed
                                                </span>
                                            <?php elseif($payment->status === 'pending'): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    Pending
                                                </span>
                                            <?php elseif($payment->status === 'failed'): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    Failed
                                                </span>
                                            <?php elseif($payment->status === 'refunded'): ?>
                                                <span class="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                                    Refunded
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Order Details -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Order Details</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Order Number</span>
                        <span class="text-gray-900 font-mono"><?php echo e($order->order_number); ?></span>
                    </div>

                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Order Date</span>
                        <span class="text-gray-900"><?php echo e($order->created_at->format('M j, Y \a\t g:i A')); ?></span>
                    </div>

                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Total Items</span>
                        <span class="text-gray-900"><?php echo e($order->items->sum('quantity')); ?></span>
                    </div>

                    <?php if($order->shipped_at): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipped Date</span>
                            <span class="text-gray-900"><?php echo e($order->shipped_at->format('M j, Y \a\t g:i A')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($order->delivered_at): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Delivered Date</span>
                            <span class="text-gray-900"><?php echo e($order->delivered_at->format('M j, Y \a\t g:i A')); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Coupon Information -->
            <?php if($order->coupon || $order->coupon_code): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Coupon Information</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <?php if($order->coupon): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Coupon Code</span>
                            <div class="text-right">
                                <span class="text-gray-900 font-mono"><?php echo e($order->coupon_code); ?></span>
                                <a href="<?php echo e(route('admin.coupons.show', $order->coupon)); ?>"
                                   class="ml-2 text-blue-600 hover:text-blue-800 text-xs">
                                    View Details
                                </a>
                            </div>
                        </div>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Coupon Name</span>
                            <span class="text-gray-900"><?php echo e($order->coupon->name); ?></span>
                        </div>

                        <?php if($order->coupon->description): ?>
                            <div class="text-sm">
                                <span class="text-gray-600">Description</span>
                                <p class="text-gray-900 mt-1"><?php echo e($order->coupon->description); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Discount Type</span>
                            <span class="text-gray-900"><?php echo e(ucfirst(str_replace('_', ' ', $order->coupon->type))); ?></span>
                        </div>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Discount Value</span>
                            <span class="text-gray-900"><?php echo e($order->coupon->formatted_value); ?></span>
                        </div>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Discount Applied</span>
                            <span class="text-red-600 font-medium">-<?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->discount_amount, 2)); ?></span>
                        </div>

                        <?php if($order->coupon->minimum_amount): ?>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Minimum Amount</span>
                                <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->coupon->minimum_amount, 2)); ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if($order->coupon->maximum_discount): ?>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Maximum Discount</span>
                                <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->coupon->maximum_discount, 2)); ?></span>
                            </div>
                        <?php endif; ?>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Coupon Status</span>
                            <span class="text-gray-900"><?php echo e($order->coupon->is_active ? 'Active' : 'Inactive'); ?></span>
                        </div>

                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Usage Statistics</span>
                            <span class="text-gray-900">
                                <?php echo e($order->coupon->used_count); ?> / <?php echo e($order->coupon->usage_limit ?? '∞'); ?> uses
                            </span>
                        </div>

                        <?php if($order->coupon->usage_limit_per_customer): ?>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Per Customer Limit</span>
                                <span class="text-gray-900"><?php echo e($order->coupon->usage_limit_per_customer); ?> uses</span>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Coupon Code</span>
                            <span class="text-gray-900 font-mono"><?php echo e($order->coupon_code); ?></span>
                        </div>
                        <div class="text-sm text-gray-500">
                            <p>Coupon details are no longer available (coupon may have been deleted)</p>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Discount Applied</span>
                            <span class="text-red-600 font-medium">-<?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->discount_amount, 2)); ?></span>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Order Summary -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Order Summary</h2>
                </div>
                <div class="px-6 py-4 space-y-3">
                    <div class="flex justify-between text-sm">
                        <span class="text-gray-600">Subtotal</span>
                        <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->subtotal, 2)); ?></span>
                    </div>

                    <?php if($order->tax_amount > 0): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Tax</span>
                            <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->tax_amount, 2)); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($order->shipping_amount > 0): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Shipping</span>
                            <span class="text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->shipping_amount, 2)); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if($order->discount_amount > 0): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-600">Discount</span>
                            <span class="text-red-600">-<?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->discount_amount, 2)); ?></span>
                        </div>
                        <?php if($order->coupon): ?>
                            <div class="text-xs text-gray-500 pl-4 space-y-1">
                                <div><strong>Coupon:</strong> <?php echo e($order->coupon_code); ?></div>
                                <div><strong>Name:</strong> <?php echo e($order->coupon->name); ?></div>
                                <?php if($order->coupon->description): ?>
                                    <div><strong>Description:</strong> <?php echo e($order->coupon->description); ?></div>
                                <?php endif; ?>
                                <div><strong>Type:</strong> <?php echo e(ucfirst(str_replace('_', ' ', $order->coupon->type))); ?></div>
                                <div><strong>Value:</strong> <?php echo e($order->coupon->formatted_value); ?></div>
                                <?php if($order->coupon->minimum_amount): ?>
                                    <div><strong>Min Amount:</strong> <?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->coupon->minimum_amount, 2)); ?></div>
                                <?php endif; ?>
                            </div>
                        <?php elseif($order->coupon_code): ?>
                            <div class="text-xs text-gray-500 pl-4">
                                Coupon: <?php echo e($order->coupon_code); ?> (Coupon details unavailable)
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <div class="border-t border-gray-200 pt-3">
                        <div class="flex justify-between">
                            <span class="text-base font-medium text-gray-900">Total</span>
                            <span class="text-base font-medium text-gray-900"><?php echo e($order->currency->symbol ?? 'R'); ?><?php echo e(number_format($order->total_amount, 2)); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Notes -->
            <?php if($order->notes): ?>
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Customer Notes</h2>
                </div>
                <div class="px-6 py-4">
                    <p class="text-sm text-gray-700 whitespace-pre-wrap"><?php echo e($order->notes); ?></p>
                </div>
            </div>
            <?php endif; ?>

            <!-- Update Order Status -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Update Order</h2>
                </div>
                <div class="px-6 py-4">
                    <!-- Display success message -->
                    <?php if(session('success')): ?>
                        <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-green-800"><?php echo e(session('success')); ?></p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Display validation errors -->
                    <?php if($errors->any()): ?>
                        <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-red-800">
                                        There were errors with your submission:
                                    </h3>
                                    <div class="mt-2 text-sm text-red-700">
                                        <ul class="list-disc list-inside space-y-1">
                                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><?php echo e($error); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo e(route('admin.orders.update', $order)); ?>" class="space-y-4">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PATCH'); ?>

                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Order Status</label>
                            <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <?php $__currentLoopData = $availableStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $statusValue => $statusLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($statusValue); ?>" <?php echo e(old('status', $order->status) === $statusValue ? 'selected' : ''); ?>>
                                        <?php echo e($statusLabel); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">Only valid status transitions are shown. Current status: <span class="font-medium"><?php echo e(ucfirst($order->status)); ?></span></p>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                            <select id="payment_status" name="payment_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent <?php $__errorArgs = ['payment_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <?php $__currentLoopData = $availablePaymentStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $paymentStatusValue => $paymentStatusLabel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($paymentStatusValue); ?>" <?php echo e(old('payment_status', $order->payment_status) === $paymentStatusValue ? 'selected' : ''); ?>>
                                        <?php echo e($paymentStatusLabel); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['payment_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                            <textarea id="notes" name="notes" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-300 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                      placeholder="Add notes about this order..."><?php echo e(old('notes', $order->notes)); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <button type="submit" class="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                            Update Order
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\web_devs\chisolution\resources\views/admin/orders/show.blade.php ENDPATH**/ ?>
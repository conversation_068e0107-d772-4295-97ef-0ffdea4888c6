@extends('layouts.dashboard')

@section('title', 'Comment Moderation')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Comment Moderation</h1>
            <p class="text-gray-600 mt-2">Review and moderate blog comments</p>
        </div>
        
        <!-- Quick Stats -->
        <div class="flex space-x-4">
            <div class="bg-yellow-100 text-yellow-800 px-3 py-2 rounded-lg text-sm font-medium">
                <span id="pending-count">{{ \App\Models\BlogComment::pending()->count() }}</span> Pending
            </div>
            <div class="bg-red-100 text-red-800 px-3 py-2 rounded-lg text-sm font-medium">
                <span id="flagged-count">{{ \App\Models\BlogComment::flagged()->count() }}</span> Flagged
            </div>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8">
            <a href="{{ route('admin.comments.index', ['status' => 'pending']) }}" 
               class="py-2 px-1 border-b-2 font-medium text-sm {{ $status === 'pending' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Pending ({{ \App\Models\BlogComment::pending()->count() }})
            </a>
            <a href="{{ route('admin.comments.index', ['status' => 'approved']) }}" 
               class="py-2 px-1 border-b-2 font-medium text-sm {{ $status === 'approved' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Approved ({{ \App\Models\BlogComment::approved()->active()->count() }})
            </a>
            <a href="{{ route('admin.comments.index', ['status' => 'flagged']) }}" 
               class="py-2 px-1 border-b-2 font-medium text-sm {{ $status === 'flagged' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                Flagged ({{ \App\Models\BlogComment::flagged()->count() }})
            </a>
            <a href="{{ route('admin.comments.index', ['status' => 'all']) }}" 
               class="py-2 px-1 border-b-2 font-medium text-sm {{ $status === 'all' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}">
                All Comments ({{ \App\Models\BlogComment::active()->count() }})
            </a>
        </nav>
    </div>

    @if($comments->count() > 0)
        <!-- Bulk Actions -->
        @if($status === 'pending')
            <div class="bg-white rounded-lg shadow mb-6 p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" id="select-all" class="form-checkbox h-4 w-4 text-blue-600">
                            <span class="ml-2 text-sm text-gray-700">Select All</span>
                        </label>
                        <span id="selected-count" class="text-sm text-gray-500">0 selected</span>
                    </div>
                    <div class="flex space-x-2">
                        <button id="bulk-approve" class="btn-primary btn-sm" disabled>
                            Bulk Approve
                        </button>
                        <button id="bulk-reject" class="btn-secondary btn-sm" disabled>
                            Bulk Reject
                        </button>
                    </div>
                </div>
            </div>
        @endif

        <!-- Comments List -->
        <div class="space-y-4">
            @foreach($comments as $comment)
                <div class="bg-white rounded-lg shadow-lg overflow-hidden comment-card" data-comment-id="{{ $comment->id }}">
                    <div class="p-6">
                        <!-- Comment Header -->
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-start space-x-4">
                                @if($status === 'pending')
                                    <input type="checkbox" class="comment-checkbox form-checkbox h-4 w-4 text-blue-600 mt-1" value="{{ $comment->id }}">
                                @endif
                                
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            {{ $comment->user->first_name }} {{ $comment->user->last_name }}
                                        </h3>
                                        <span class="text-sm text-gray-500">{{ $comment->time_ago }}</span>
                                        
                                        <!-- Status Badges -->
                                        @if($comment->is_flagged)
                                            <span class="px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">Flagged</span>
                                        @endif
                                        @if($comment->rating)
                                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                {{ $comment->rating }}/5 ⭐
                                            </span>
                                        @endif
                                        @if($comment->has_attachments)
                                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                {{ $comment->attachment_count }} files
                                            </span>
                                        @endif
                                    </div>
                                    
                                    <p class="text-sm text-gray-600 mb-2">
                                        On: <a href="{{ route('blog.show', $comment->blogPost->slug) }}" class="text-blue-600 hover:text-blue-800" target="_blank">
                                            {{ $comment->blogPost->title }}
                                        </a>
                                    </p>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="flex space-x-2">
                                @if(!$comment->is_approved && !$comment->is_deleted)
                                    <button class="approve-btn btn-sm bg-green-600 text-white hover:bg-green-700" 
                                            data-comment-id="{{ $comment->id }}">
                                        Approve
                                    </button>
                                    <button class="reject-btn btn-sm bg-red-600 text-white hover:bg-red-700" 
                                            data-comment-id="{{ $comment->id }}">
                                        Reject
                                    </button>
                                @endif
                                <a href="{{ route('admin.comments.show', $comment->uuid) }}" 
                                   class="btn-sm btn-secondary">
                                    View Details
                                </a>
                            </div>
                        </div>

                        <!-- Comment Content -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="prose prose-sm max-w-none">
                                {!! nl2br(e($comment->content)) !!}
                            </div>
                        </div>

                        <!-- Attachments Preview -->
                        @if($comment->has_attachments)
                            <div class="mb-4">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Attachments</h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach($comment->getOptimizedAttachments() as $attachment)
                                        <div class="flex items-center space-x-2 bg-gray-100 rounded px-3 py-1">
                                            <span class="text-xs text-gray-600">{{ basename($attachment['original']) }}</span>
                                            <a href="{{ route('comments.download', ['comment' => $comment->uuid, 'filename' => $attachment['original']]) }}" 
                                               class="text-blue-600 hover:text-blue-800">
                                                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        <!-- Admin Notes -->
                        @if($comment->admin_notes)
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <h4 class="text-sm font-medium text-blue-800 mb-1">Admin Notes</h4>
                                <p class="text-sm text-blue-700">{{ $comment->admin_notes }}</p>
                            </div>
                        @endif

                        <!-- Moderation Info -->
                        @if($comment->is_approved && $comment->approvedBy)
                            <div class="text-xs text-gray-500 mt-2">
                                Approved by {{ $comment->approvedBy->first_name }} {{ $comment->approvedBy->last_name }} 
                                on {{ $comment->approved_at->format('M j, Y g:i A') }}
                            </div>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Pagination -->
        <div class="mt-8">
            {{ $comments->appends(['status' => $status])->links() }}
        </div>
    @else
        <div class="bg-white rounded-lg shadow p-12 text-center">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Comments Found</h3>
            <p class="text-gray-600">
                @if($status === 'pending')
                    No comments are currently pending approval.
                @elseif($status === 'flagged')
                    No comments have been flagged for review.
                @else
                    No comments match the current filter.
                @endif
            </p>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('select-all');
    const commentCheckboxes = document.querySelectorAll('.comment-checkbox');
    const selectedCountSpan = document.getElementById('selected-count');
    const bulkApproveBtn = document.getElementById('bulk-approve');
    const bulkRejectBtn = document.getElementById('bulk-reject');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            commentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });

        commentCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkActions);
        });
    }

    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.comment-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        if (selectedCountSpan) selectedCountSpan.textContent = `${count} selected`;
        
        if (bulkApproveBtn) bulkApproveBtn.disabled = count === 0;
        if (bulkRejectBtn) bulkRejectBtn.disabled = count === 0;
    }

    // Individual approve/reject buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('approve-btn')) {
            const commentId = e.target.dataset.commentId;
            moderateComment(commentId, 'approve');
        }
        
        if (e.target.classList.contains('reject-btn')) {
            const commentId = e.target.dataset.commentId;
            moderateComment(commentId, 'reject');
        }
    });

    // Bulk actions
    if (bulkApproveBtn) {
        bulkApproveBtn.addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.comment-checkbox:checked')).map(cb => cb.value);
            bulkModerate(selectedIds, 'approve');
        });
    }

    if (bulkRejectBtn) {
        bulkRejectBtn.addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.comment-checkbox:checked')).map(cb => cb.value);
            bulkModerate(selectedIds, 'reject');
        });
    }

    function moderateComment(commentId, action) {
        const url = `/admin/comments/${commentId}/${action}`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                // Remove the comment card or update its status
                const commentCard = document.querySelector(`[data-comment-id="${commentId}"]`);
                if (commentCard) {
                    commentCard.style.opacity = '0.5';
                    setTimeout(() => commentCard.remove(), 1000);
                }
                updateStats();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred.', 'error');
        });
    }

    function bulkModerate(commentIds, action) {
        if (commentIds.length === 0) return;
        
        const url = `/admin/comments/bulk-${action}`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ comment_ids: commentIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                // Reload the page to reflect changes
                setTimeout(() => window.location.reload(), 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred.', 'error');
        });
    }

    function updateStats() {
        fetch('/admin/comments/stats/data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const pendingCount = document.getElementById('pending-count');
                const flaggedCount = document.getElementById('flagged-count');
                
                if (pendingCount) pendingCount.textContent = data.stats.pending;
                if (flaggedCount) flaggedCount.textContent = data.stats.flagged;
            }
        })
        .catch(error => console.error('Error updating stats:', error));
    }

    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }
});
</script>
@endpush
@endsection

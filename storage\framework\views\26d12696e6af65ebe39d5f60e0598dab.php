<?php $__env->startSection('title', __('careers.page_title')); ?>
<?php $__env->startSection('meta_description', __('careers.meta_description')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-20 lg:py-32">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                <?php echo e(__('careers.hero_title')); ?>

            </h1>
            <p class="text-xl lg:text-2xl text-blue-100 mb-8">
                <?php echo e(__('careers.hero_subtitle')); ?>

            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="#open-positions" class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                    <?php echo e(__('careers.view_open_positions')); ?>

                </a>
                <a href="#company-culture" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                    <?php echo e(__('careers.learn_about_culture')); ?>

                </a>
            </div>
        </div>
    </div>
</section>

<!-- Job Search and Filters -->
<section class="py-12 bg-gray-50">
    <div class="container mx-auto px-4">
        <form method="GET" class="bg-white rounded-lg shadow-md p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e(__('careers.search_jobs')); ?></label>
                    <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>"
                           placeholder="<?php echo e(__('careers.job_title_keywords')); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <!-- Employment Type -->
                <div>
                    <label for="employment_type" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e(__('careers.employment_type')); ?></label>
                    <select name="employment_type" id="employment_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all"><?php echo e(__('careers.all_types')); ?></option>
                        <?php $__currentLoopData = $employmentTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(request('employment_type') === $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Experience Level -->
                <div>
                    <label for="experience_level" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e(__('careers.experience_level')); ?></label>
                    <select name="experience_level" id="experience_level" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all"><?php echo e(__('careers.all_levels')); ?></option>
                        <?php $__currentLoopData = $experienceLevels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($key); ?>" <?php echo e(request('experience_level') === $key ? 'selected' : ''); ?>><?php echo e($label); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>

                <!-- Location -->
                <div>
                    <label for="location" class="block text-sm font-medium text-gray-700 mb-1"><?php echo e(__('careers.location')); ?></label>
                    <select name="location" id="location" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="all"><?php echo e(__('careers.all_locations')); ?></option>
                        <?php $__currentLoopData = $locations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $location): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($location); ?>" <?php echo e(request('location') === $location ? 'selected' : ''); ?>><?php echo e($location); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
            </div>

            <div class="flex justify-between items-center">
                <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    <?php echo e(__('careers.search_jobs_button')); ?>

                </button>
                <a href="<?php echo e(route('careers.index', ['locale' => app()->getLocale()])); ?>" class="text-gray-600 hover:text-gray-800"><?php echo e(__('careers.clear_filters')); ?></a>
            </div>
        </form>
    </div>
</section>

<!-- Open Positions -->
<section id="open-positions" class="py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4"><?php echo e(__('careers.open_positions')); ?></h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                <?php echo e(__('careers.open_positions_subtitle')); ?>

            </p>
        </div>

        <?php if($jobs->count() > 0): ?>
            <div class="grid gap-6">
                <?php $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 border border-gray-200">
                        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <h3 class="text-xl font-semibold text-gray-900 mb-2">
                                        <a href="<?php echo e(route('careers.show', ['locale' => app()->getLocale(), 'job' => $job])); ?>" class="hover:text-blue-600 transition-colors">
                                            <?php echo e($job->title); ?>

                                        </a>
                                    </h3>
                                    <?php if($job->is_featured): ?>
                                        <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full"><?php echo e(__('careers.featured')); ?></span>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="flex flex-wrap gap-4 text-sm text-gray-600 mb-3">
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?php echo e($job->department); ?>

                                    </span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?php echo e($job->location); ?>

                                        <?php if($job->is_remote): ?>
                                            <span class="ml-1 text-green-600">(<?php echo e(__('careers.remote')); ?>)</span>
                                        <?php endif; ?>
                                    </span>
                                    <span class="flex items-center">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <?php echo e(ucfirst(str_replace('-', ' ', $job->employment_type))); ?>

                                    </span>
                                </div>

                                <p class="text-gray-700 mb-4 line-clamp-2">
                                    <?php echo e(Str::limit(strip_tags($job->description), 150)); ?>

                                </p>

                                <div class="flex items-center justify-between">
                                    <span class="text-lg font-semibold text-green-600">
                                        <?php echo e($job->formatted_salary); ?>

                                    </span>
                                    <?php if($job->application_deadline): ?>
                                        <span class="text-sm text-gray-500">
                                            <?php echo e(__('careers.apply_by')); ?> <?php echo e($job->application_deadline->format('M j, Y')); ?>

                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="mt-4 lg:mt-0 lg:ml-6">
                                <a href="<?php echo e(route('careers.show', ['locale' => app()->getLocale(), 'job' => $job])); ?>"
                                   class="inline-block bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors text-center w-full lg:w-auto">
                                    <?php echo e(__('careers.view_details')); ?>

                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <!-- Pagination -->
            <div class="mt-12">
                <?php echo e($jobs->links()); ?>

            </div>
        <?php else: ?>
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6M8 8v10a2 2 0 002 2h4a2 2 0 002-2V8M8 8V6a2 2 0 012-2h4a2 2 0 012-2V8" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900"><?php echo e(__('careers.no_jobs_title')); ?></h3>
                <p class="mt-1 text-sm text-gray-500">
                    <?php echo e(__('careers.no_jobs_message')); ?>

                </p>
                <div class="mt-6">
                    <a href="<?php echo e(route('careers.index', ['locale' => app()->getLocale()])); ?>" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                        <?php echo e(__('careers.view_all_jobs')); ?>

                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Company Culture -->
<section id="company-culture" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Why Work With Us?</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                We believe in creating an environment where our team can thrive, innovate, and make a real impact.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Collaborative Team</h3>
                <p class="text-gray-600">Work with talented, passionate individuals who support each other's growth and success.</p>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Great Benefits</h3>
                <p class="text-gray-600">Competitive salary, health insurance, flexible working hours, and professional development opportunities.</p>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6 text-center">
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <svg class="w-6 h-6 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Innovation Focus</h3>
                <p class="text-gray-600">Work on cutting-edge projects using the latest technologies and methodologies in the industry.</p>
            </div>
        </div>
    </div>
</section>

<!-- Application Status Check -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-4 text-center">Check Application Status</h3>
            <form id="careerStatusForm" onsubmit="checkCareerStatus(event)">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="career_reference_number" class="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                    <input type="text" name="reference_number" id="career_reference_number" required
                           placeholder="JOB-XXXXXXXX"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="mb-4">
                    <label for="career_email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                    <input type="email" name="email" id="career_email" required
                           placeholder="<EMAIL>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" id="careerStatusButton" class="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition-colors">
                    Check Status
                </button>
            </form>

            <div id="careerStatusResult" class="mt-4 hidden"></div>
        </div>
    </div>
</section>

<script>
function checkCareerStatus(event) {
    event.preventDefault();

    const referenceNumber = document.getElementById('career_reference_number').value.trim();
    const email = document.getElementById('career_email').value.trim();
    const resultDiv = document.getElementById('careerStatusResult');
    const button = document.getElementById('careerStatusButton');

    if (!referenceNumber || !email) {
        showCareerStatusResult('Please enter both reference number and email address.', 'error');
        return;
    }

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Checking...';
    showCareerStatusResult('Checking status...', 'loading');

    // Track form interaction start
    if (typeof trackFormInteraction === 'function') {
        trackFormInteraction('career_status_check_form', 'submit_start', true, {
            reference_number_length: referenceNumber.length,
            email_domain: email.split('@')[1],
            timestamp: new Date().toISOString()
        });
    }

    // Create form data
    const formData = new FormData();
    formData.append('reference_number', referenceNumber);
    formData.append('email', email);
    formData.append('_token', '<?php echo e(csrf_token()); ?>');

    fetch('<?php echo e(route("careers.check-status", ["locale" => app()->getLocale()])); ?>', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        if (response.redirected) {
            // Handle redirect to status page
            window.location.href = response.url;
            return;
        }
        return response.text();
    })
    .then(data => {
        if (typeof data === 'string' && data.includes('Application Status Check')) {
            // Success - redirect occurred, handle it
            showCareerStatusResult('Application found! Redirecting...', 'success');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showCareerStatusResult('Application not found with the provided details.', 'error');
        }

        // Track form interaction result
        if (typeof trackFormInteraction === 'function') {
            trackFormInteraction('career_status_check_form', 'submit_complete', true, {
                reference_number: referenceNumber,
                email_domain: email.split('@')[1],
                timestamp: new Date().toISOString()
            });
        }
    })
    .catch(error => {
        console.error('Career status check error:', error);
        showCareerStatusResult('An error occurred while checking status. Please try again.', 'error');

        // Track error
        if (typeof trackFormInteraction === 'function') {
            trackFormInteraction('career_status_check_form', 'submit_error', false, {
                reference_number: referenceNumber,
                error_type: 'network_error',
                error_message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    })
    .finally(() => {
        // Reset button state
        button.disabled = false;
        button.innerHTML = 'Check Status';
    });
}

function showCareerStatusResult(content, type) {
    const resultDiv = document.getElementById('careerStatusResult');
    resultDiv.classList.remove('hidden');

    let icon;
    switch (type) {
        case 'error':
            icon = '<svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
            resultDiv.innerHTML = `<div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-center"><div class="flex-shrink-0">${icon}</div><div class="text-red-700">${content}</div></div></div>`;
            break;
        case 'loading':
            icon = '<svg class="animate-spin w-5 h-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
            resultDiv.innerHTML = `<div class="bg-blue-50 border border-blue-200 rounded-lg p-4"><div class="flex items-center"><div class="flex-shrink-0">${icon}</div><div class="text-blue-700">${content}</div></div></div>`;
            break;
        case 'success':
            icon = '<svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>';
            resultDiv.innerHTML = `<div class="bg-green-50 border border-green-200 rounded-lg p-4"><div class="flex items-center"><div class="flex-shrink-0">${icon}</div><div class="text-green-700">${content}</div></div></div>`;
            break;
        default:
            resultDiv.innerHTML = content;
    }
}

// Global function for visitor analytics tracking
function trackFormInteraction(formId, action, success, data = {}) {
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackFormInteraction) {
        window.visitorAnalytics.trackFormInteraction(formId, action, success, data);
    }
}

// Initialize visitor analytics tracking on page load
document.addEventListener('DOMContentLoaded', function() {
    // Track page visit for careers page
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackPageVisit) {
        window.visitorAnalytics.trackPageVisit('Careers Page', {
            page_type: 'careers_listing',
            has_status_check_form: true,
            timestamp: new Date().toISOString()
        });
    }

    // Track form view for career status check form
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackFormInteraction) {
        window.visitorAnalytics.trackFormInteraction('career_status_check_form', 'view', true, {
            form_location: 'careers_page',
            timestamp: new Date().toISOString()
        });
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\web_devs\chisolution\resources\views/careers/index.blade.php ENDPATH**/ ?>
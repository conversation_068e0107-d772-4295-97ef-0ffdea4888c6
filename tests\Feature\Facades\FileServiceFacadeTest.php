<?php

namespace Tests\Feature\Facades;

use App\Facades\FileService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;
use Tests\TestCase;

use PHPUnit\Framework\Attributes\Test;
class FileServiceFacadeTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        Config::set('file.max_file_size', 50 * 1024 * 1024);
        Config::set('file.allowed_mimes', [
            'application/pdf',
            'text/plain',
            'text/csv',
            'application/zip',
        ]);
        Config::set('file.allowed_extensions', ['pdf', 'txt', 'csv', 'zip']);
        Config::set('file.virus_scan.enabled', false);
        Config::set('file.storage.disk', 'testing');
        Config::set('file.storage.path', 'files');
        Config::set('file.logging.enabled', false);
        
        Config::set('filesystems.disks.testing', [
            'driver' => 'local',
            'root' => storage_path('app/testing'),
        ]);
        
        Storage::fake('testing');
    }
    
    protected function tearDown(): void
    {
        Storage::disk('testing')->deleteDirectory('');
        parent::tearDown();
    }
    #[Test]
    public function facade_can_process_uploaded_file()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Facade test content');
        
        $file = new UploadedFile($tempPath, 'facade.txt', 'text/plain', null, true);
        
        $result = FileService::processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('file_path', $result);
        $this->assertEquals('facade.txt', $result['file_info']['original_name']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_quick_upload()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Quick facade test');
        
        $file = new UploadedFile($tempPath, 'quick.txt', 'text/plain', null, true);
        
        $result = FileService::quickUpload($file, 'facade-quick');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('facade-quick', $result['file_path']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_secure_upload()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Secure facade test');
        
        $file = new UploadedFile($tempPath, 'secure.txt', 'text/plain', null, true);
        
        $result = FileService::secureUpload($file, 'facade-secure');
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('facade-secure', $result['file_path']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_validate_file()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Validation facade test');
        
        $file = new UploadedFile($tempPath, 'validate.txt', 'text/plain', null, true);
        
        $result = FileService::validateFile($file);
        
        $this->assertTrue($result['valid']);
        $this->assertEmpty($result['errors']);
        $this->assertEquals('validate.txt', $result['file_info']['original_name']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_sanitize_filename()
    {
        $dangerous = '../../../etc/passwd.txt';
        $sanitized = FileService::sanitizeFilename($dangerous);
        
        $this->assertStringNotContainsString('..', $sanitized);
        $this->assertStringNotContainsString('/', $sanitized);
        $this->assertStringContainsString('.txt', $sanitized);
    }
    #[Test]
    public function facade_can_scan_for_viruses()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_file');
        file_put_contents($tempPath, 'Clean file content');
        
        $result = FileService::scanForViruses($tempPath);
        
        $this->assertTrue($result['clean']);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('scanner', $result);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_remove_metadata()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($tempPath, '%PDF-1.4' . str_repeat('a', 100));
        
        $result = FileService::removeMetadata($tempPath);
        
        $this->assertTrue($result);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_process_archive()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_zip');
        file_put_contents($tempPath, 'PK' . str_repeat('a', 100));
        
        $result = FileService::processArchive($tempPath);
        
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('extracted_files', $result);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_extract_text_content()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Text extraction facade test');
        
        $result = FileService::extractTextContent($tempPath);
        
        $this->assertTrue($result['success']);
        $this->assertStringContainsString('extraction facade', $result['text']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_get_file_url()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'URL facade test');
        
        $file = new UploadedFile($tempPath, 'url.txt', 'text/plain', null, true);
        $result = FileService::processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        
        $url = FileService::getFileUrl($result['file_path']);
        
        $this->assertIsString($url);
        $this->assertStringContainsString($result['file_path'], $url);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_delete_file()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Delete facade test');
        
        $file = new UploadedFile($tempPath, 'delete.txt', 'text/plain', null, true);
        $result = FileService::processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        $this->assertTrue(Storage::disk('testing')->exists($result['file_path']));
        
        $deleted = FileService::deleteFile($result['file_path']);
        
        $this->assertTrue($deleted);
        $this->assertFalse(Storage::disk('testing')->exists($result['file_path']));
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_can_get_file_info()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Info facade test');
        
        $file = new UploadedFile($tempPath, 'info.txt', 'text/plain', null, true);
        $result = FileService::processUploadedFile($file);
        
        $this->assertTrue($result['success']);
        
        $info = FileService::getFileInfo($result['file_path']);
        
        $this->assertTrue($info['exists']);
        $this->assertArrayHasKey('size', $info);
        $this->assertArrayHasKey('last_modified', $info);
        $this->assertArrayHasKey('mime_type', $info);
        $this->assertArrayHasKey('url', $info);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_handles_invalid_files()
    {
        $file = UploadedFile::fake()->create('malware.exe', 1024, 'application/octet-stream');
        
        $result = FileService::validateFile($file);
        
        $this->assertFalse($result['valid']);
        $this->assertNotEmpty($result['errors']);
    }
    #[Test]
    public function facade_handles_nonexistent_files()
    {
        $info = FileService::getFileInfo('non-existent-file.txt');
        
        $this->assertFalse($info['exists']);
        $this->assertEquals('File not found', $info['message']);
        
        $deleted = FileService::deleteFile('non-existent-file.txt');
        $this->assertFalse($deleted);
    }
    #[Test]
    public function facade_methods_return_consistent_results()
    {
        $tempPath = tempnam(sys_get_temp_dir(), 'test_txt');
        file_put_contents($tempPath, 'Consistency test');
        
        $file = new UploadedFile($tempPath, 'consistent.txt', 'text/plain', null, true);
        
        // Test that facade and direct service calls return same results
        $facadeResult = FileService::validateFile($file);
        $serviceResult = app(\App\Services\FileService::class)->validateFile($file);
        
        $this->assertEquals($facadeResult['valid'], $serviceResult['valid']);
        $this->assertEquals($facadeResult['file_info']['original_name'], $serviceResult['file_info']['original_name']);
        
        unlink($tempPath);
    }
    #[Test]
    public function facade_logging_methods_work()
    {
        // These methods don't return values, just ensure they don't throw exceptions
        FileService::logProcessing('Test message', ['context' => 'test']);
        FileService::logError('Test error', ['error' => 'test']);
        FileService::logSecurityEvent('Test security event', ['event' => 'test']);
        
        $this->assertTrue(true); // If we get here, no exceptions were thrown
    }
    #[Test]
    public function facade_works_with_different_file_types()
    {
        // Test PDF
        $pdfPath = tempnam(sys_get_temp_dir(), 'test_pdf');
        file_put_contents($pdfPath, '%PDF-1.4' . str_repeat('a', 1000));
        $pdfFile = new UploadedFile($pdfPath, 'test.pdf', 'application/pdf', null, true);
        
        $pdfResult = FileService::processUploadedFile($pdfFile);
        $this->assertTrue($pdfResult['success']);
        
        // Test CSV
        $csvPath = tempnam(sys_get_temp_dir(), 'test_csv');
        file_put_contents($csvPath, "Name,Email\nJohn,<EMAIL>");
        $csvFile = new UploadedFile($csvPath, 'test.csv', 'text/csv', null, true);
        
        $csvResult = FileService::processUploadedFile($csvFile);
        $this->assertTrue($csvResult['success']);
        
        unlink($pdfPath);
        unlink($csvPath);
    }
}

<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\ProjectApplicationController;
use App\Http\Controllers\CareerController;

use App\Http\Controllers\CartController;
use App\Http\Controllers\CheckoutController;
use App\Http\Controllers\WebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Language switching route (no prefix)
Route::get('/language/{locale}', function ($locale) {
    if (in_array($locale, ['en', 'fr', 'es'])) {
        session(['locale' => $locale]);
        return redirect()->back();
    }
    return redirect()->back();
})->name('language.switch');

// Admin routes (no localization)
Route::prefix('admin')->name('admin.')->middleware(['auth', 'role:admin,staff'])->group(function () {
    // Dashboard
    Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard.alt');

    // Categories Management - Requires categories permissions
    Route::middleware(['permission:categories,read'])->group(function () {
        Route::get('/categories', [App\Http\Controllers\Admin\CategoryController::class, 'index'])->name('categories.index');
        Route::get('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'show'])->name('categories.show');
        Route::get('/categories-api', [App\Http\Controllers\Admin\CategoryController::class, 'getCategories'])->name('categories.api');
    });
    Route::middleware(['permission:categories,create'])->group(function () {
        Route::get('/categories/create', [App\Http\Controllers\Admin\CategoryController::class, 'create'])->name('categories.create');
        Route::post('/categories', [App\Http\Controllers\Admin\CategoryController::class, 'store'])->name('categories.store');
    });
    Route::middleware(['permission:categories,update'])->group(function () {
        Route::get('/categories/{category}/edit', [App\Http\Controllers\Admin\CategoryController::class, 'edit'])->name('categories.edit');
        Route::put('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('categories.update');
        Route::patch('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'update'])->name('categories.update');
        Route::post('/categories/reorder', [App\Http\Controllers\Admin\CategoryController::class, 'reorder'])->name('categories.reorder');
        Route::post('/categories/{category}/toggle-featured', [App\Http\Controllers\Admin\CategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');
    });
    Route::middleware(['permission:categories,delete'])->group(function () {
        Route::delete('/categories/{category}', [App\Http\Controllers\Admin\CategoryController::class, 'destroy'])->name('categories.destroy');
    });

    // Products Management - Requires products permissions
    Route::middleware(['permission:products,read'])->group(function () {
        Route::get('/products', [App\Http\Controllers\Admin\ProductController::class, 'index'])->name('products.index');
        Route::get('/products/{product}', [App\Http\Controllers\Admin\ProductController::class, 'show'])->name('products.show');
    });
    Route::middleware(['permission:products,create'])->group(function () {
        Route::get('/products/create', [App\Http\Controllers\Admin\ProductController::class, 'create'])->name('products.create');
        Route::post('/products', [App\Http\Controllers\Admin\ProductController::class, 'store'])->name('products.store');
    });
    Route::middleware(['permission:products,update'])->group(function () {
        Route::get('/products/{product}/edit', [App\Http\Controllers\Admin\ProductController::class, 'edit'])->name('products.edit');
        Route::put('/products/{product}', [App\Http\Controllers\Admin\ProductController::class, 'update'])->name('products.update');
        Route::patch('/products/{product}', [App\Http\Controllers\Admin\ProductController::class, 'update'])->name('products.update');
        Route::post('/products/{product}/images', [App\Http\Controllers\Admin\ProductController::class, 'uploadImages'])->name('products.images.upload');
        Route::delete('/products/{product}/images/{image}', [App\Http\Controllers\Admin\ProductController::class, 'deleteImage'])->name('products.images.delete');
        Route::post('/products/{product}/toggle-featured', [App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');
    });
    Route::middleware(['permission:products,delete'])->group(function () {
        Route::delete('/products/{product}', [App\Http\Controllers\Admin\ProductController::class, 'destroy'])->name('products.destroy');
    });

    // Coupons Management
    Route::resource('coupons', App\Http\Controllers\Admin\CouponController::class);
    Route::post('/coupons/{coupon}/toggle', [App\Http\Controllers\Admin\CouponController::class, 'toggle'])->name('coupons.toggle');

    // Orders Management
    Route::resource('orders', App\Http\Controllers\Admin\OrderController::class)->only(['index', 'show', 'update']);
    Route::patch('/orders/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.status');

    // Users Management - Requires users permissions
    Route::middleware(['permission:users,read'])->group(function () {
        Route::get('/users', [App\Http\Controllers\Admin\UserController::class, 'index'])->name('users.index');
        Route::get('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'show'])->name('users.show');
    });
    Route::middleware(['permission:users,create'])->group(function () {
        Route::get('/users/create', [App\Http\Controllers\Admin\UserController::class, 'create'])->name('users.create');
        Route::post('/users', [App\Http\Controllers\Admin\UserController::class, 'store'])->name('users.store');
    });
    Route::middleware(['permission:users,update'])->group(function () {
        Route::get('/users/{user}/edit', [App\Http\Controllers\Admin\UserController::class, 'edit'])->name('users.edit');
        Route::put('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('users.update');
        Route::patch('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'update'])->name('users.update');
        Route::post('users/{user}/toggle', [App\Http\Controllers\Admin\UserController::class, 'toggle'])->name('users.toggle');
        Route::post('users/{user}/verify-email', [App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('users.verify-email');
        Route::post('users/{user}/resend-verification', [App\Http\Controllers\Admin\UserController::class, 'resendVerification'])->name('users.resend-verification');
    });
    Route::middleware(['permission:users,delete'])->group(function () {
        Route::delete('/users/{user}', [App\Http\Controllers\Admin\UserController::class, 'destroy'])->name('users.destroy');
    });

    /*
    // Login History Management - Admin only
    Route::middleware(['permission:users,read'])->prefix('users/{user}/login-history')->name('users.login-history.')->group(function () {
        Route::get('/', [App\Http\Controllers\LoginHistoryController::class, 'show'])->name('index');
        Route::get('/data', [App\Http\Controllers\LoginHistoryController::class, 'data'])->name('data');
        Route::get('/stats', [App\Http\Controllers\LoginHistoryController::class, 'stats'])->name('stats');
        Route::get('/export', [App\Http\Controllers\LoginHistoryController::class, 'export'])->name('export');
        Route::post('/{loginHistory:public_id}/mark-suspicious', [App\Http\Controllers\LoginHistoryController::class, 'markSuspicious'])->name('mark-suspicious');
        Route::delete('/{loginHistory:public_id}', [App\Http\Controllers\LoginHistoryController::class, 'destroy'])->name('destroy');
    });
    */

    // Permission Management - Admin only
    Route::middleware(['role:admin'])->prefix('permissions')->name('permissions.')->group(function () {
        // Permission Dashboard
        Route::get('/', [App\Http\Controllers\Admin\PermissionController::class, 'index'])->name('index');

        // Role Management
        Route::get('/roles', [App\Http\Controllers\Admin\PermissionController::class, 'roles'])->name('roles');
        Route::put('/roles/{role}/permissions', [App\Http\Controllers\Admin\PermissionController::class, 'updateRolePermissions'])->name('roles.permissions.update');

        // User Permission Management
        Route::get('/users/{user}', [App\Http\Controllers\Admin\PermissionController::class, 'show'])->name('users.show');
        Route::put('/users/{user}/role', [App\Http\Controllers\Admin\PermissionController::class, 'updateUserRole'])->name('users.role.update');
        Route::post('/users/bulk-role-update', [App\Http\Controllers\Admin\PermissionController::class, 'bulkUpdateRoles'])->name('users.bulk-role-update');

        // Permission Matrix API
        Route::get('/matrix', [App\Http\Controllers\Admin\PermissionController::class, 'getPermissionMatrixApi'])->name('matrix');
        Route::get('/available', [App\Http\Controllers\Admin\PermissionController::class, 'getAvailablePermissionsApi'])->name('available');
    });

    // Projects Management
    Route::resource('projects', App\Http\Controllers\Admin\ProjectController::class);
    Route::post('/projects/{project}/toggle-published', [App\Http\Controllers\Admin\ProjectController::class, 'togglePublished'])->name('projects.toggle-published');
    Route::post('/projects/{project}/toggle-featured', [App\Http\Controllers\Admin\ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');

    // Project Applications Management
    Route::resource('project-applications', App\Http\Controllers\Admin\ProjectApplicationController::class);
    Route::patch('/project-applications/{projectApplication}/status', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'updateStatus'])->name('project-applications.update-status');
    Route::get('/project-applications/{projectApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'downloadAttachment'])->name('project-applications.download');
    Route::get('/project-applications-statistics', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'getStatistics'])->name('project-applications.statistics');

    // Jobs Management
    Route::resource('jobs', App\Http\Controllers\Admin\JobController::class);
    Route::post('/jobs/{job}/toggle-featured', [App\Http\Controllers\Admin\JobController::class, 'toggleFeatured'])->name('jobs.toggle-featured');
    Route::post('/jobs/{job}/toggle-active', [App\Http\Controllers\Admin\JobController::class, 'toggleActive'])->name('jobs.toggle-active');

    // Job Applications Management
    Route::resource('job-applications', App\Http\Controllers\Admin\JobApplicationController::class)->only(['index', 'show', 'update', 'destroy']);
    Route::patch('/job-applications/{jobApplication}/status', [App\Http\Controllers\Admin\JobApplicationController::class, 'updateStatus'])->name('job-applications.update-status');
    Route::get('/job-applications/{jobApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\JobApplicationController::class, 'downloadAttachment'])->name('job-applications.download');
    Route::get('/job-applications-statistics', [App\Http\Controllers\Admin\JobApplicationController::class, 'getStatistics'])->name('job-applications.statistics');

    // Activity Logs - Admin and Staff only
    Route::get('/activity-logs', [App\Http\Controllers\Admin\ActivityLogController::class, 'index'])->name('activity-logs.index');
    Route::get('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'show'])->name('activity-logs.show');
    Route::delete('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'destroy'])->name('activity-logs.destroy');
    Route::get('/activity-logs-data', [App\Http\Controllers\Admin\ActivityLogController::class, 'data'])->name('activity-logs.data');
    Route::delete('/activity-logs/bulk-delete', [App\Http\Controllers\Admin\ActivityLogController::class, 'bulkDelete'])->name('activity-logs.bulk-delete');
    Route::delete('/activity-logs/clean-old', [App\Http\Controllers\Admin\ActivityLogController::class, 'cleanOldLogs'])->name('activity-logs.clean-old');
    Route::get('/activity-logs/export', [App\Http\Controllers\Admin\ActivityLogController::class, 'export'])->name('activity-logs.export');
    Route::get('/activity-logs-stats', [App\Http\Controllers\Admin\ActivityLogController::class, 'stats'])->name('activity-logs.stats');
});

// Redirect root to default language (outside of any locale group)
Route::get('/', function () {
    return redirect('/en/');
});

// Define supported locales
$supportedLocales = ['en', 'fr', 'es'];

/**
 * Auto-redirect middleware for non-localized URLs
 *
 * This middleware group handles URLs without language prefixes (e.g., /about, /services)
 * and automatically redirects them to the appropriate localized version based on:
 *
 * 1. User's session language preference (if previously set)
 * 2. Authenticated user's preferred language setting
 * 3. Browser's Accept-Language header
 * 4. Geographic location (via CloudFlare or other headers)
 * 5. Fallback to English (/en/) as default
 *
 * All redirects use 301 (Moved Permanently) status for SEO benefits.
 * This ensures search engines understand the canonical URLs and users
 * can manually type URLs without language prefixes.
 */
Route::middleware('auto.localize')->group(function () {
    // Static pages without locale prefix - redirect to localized version
    Route::get('/about', function () {
        return redirect()->route('about', ['locale' => app('auto.locale')], 301);
    });

    Route::get('/contact', function () {
        return redirect()->route('contact', ['locale' => app('auto.locale')], 301);
    });

    // Services routes without locale prefix - redirect to localized version
    Route::prefix('services')->group(function () {
        Route::get('/', function () {
            return redirect()->route('services.index', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/web-development', function () {
            return redirect()->route('services.web-development', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/mobile-app-development', function () {
            return redirect()->route('services.mobile-app-development', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/ecommerce-development', function () {
            return redirect()->route('services.ecommerce-development', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/digital-marketing', function () {
            return redirect()->route('services.digital-marketing', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/seo-services', function () {
            return redirect()->route('services.seo-services', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/maintenance-support', function () {
            return redirect()->route('services.maintenance-support', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/data-analytics', function () {
            return redirect()->route('services.data-analytics', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/accounting-services', function () {
            return redirect()->route('services.accounting-services', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/ai-services', function () {
            return redirect()->route('services.ai-services', ['locale' => app('auto.locale')], 301);
        });
    });

    // Projects routes without locale prefix - redirect to localized version
    Route::get('/projects', function () {
        return redirect()->route('projects.index', ['locale' => app('auto.locale')], 301);
    });

    // Shop routes without locale prefix - redirect to localized version
    Route::prefix('shop')->group(function () {
        Route::get('/', function () {
            return redirect()->route('shop.index', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/category/{category}', function ($category) {
            return redirect()->route('shop.category', ['category' => $category, 'locale' => app('auto.locale')], 301);
        });

        Route::get('/product/{slug}', function ($slug) {
            return redirect()->route('shop.product', ['product' => $slug, 'locale' => app('auto.locale')], 301);
        });
    });

    // Blog routes without locale prefix - redirect to localized version
    Route::prefix('blog')->group(function () {
        Route::get('/', function () {
            return redirect()->route('blog.index', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/{post}', function ($post) {
            return redirect()->route('blog.show', ['post' => $post, 'locale' => app('auto.locale')], 301);
        });
    });

    // Apply routes without locale prefix - redirect to localized version
    Route::get('/apply', function () {
        return redirect()->route('apply.project', ['locale' => app('auto.locale')], 301);
    });

    // Careers routes without locale prefix - redirect to localized version
    Route::prefix('careers')->group(function () {
        Route::get('/', function () {
            return redirect()->route('careers.index', ['locale' => app('auto.locale')], 301);
        });

        Route::get('/job/{job}', function ($job) {
            return redirect()->route('careers.show', ['job' => $job, 'locale' => app('auto.locale')], 301);
        });
    });

    // Cart routes without locale prefix - redirect to localized version
    Route::get('/cart', function () {
        return redirect()->route('cart.index', ['locale' => app('auto.locale')], 301);
    });

    Route::post('/cart/coupon', function () {
        return redirect()->route('cart.coupon', ['locale' => app('auto.locale')], 301);
    });

    Route::delete('/cart/coupon', function () {
        return redirect()->route('cart.coupon.remove', ['locale' => app('auto.locale')], 301);
    });
});

// Localized routes group
Route::prefix('{locale}')->where(['locale' => implode('|', $supportedLocales)])->group(function () {
    // Home page
    Route::get('/', [HomeController::class, 'index'])->name('home');

    // Static pages
    Route::get('/about', function () {
        return view('pages.about');
    })->name('about');

    Route::get('/contact', function () {
        return view('pages.contact');
    })->name('contact');

    Route::post('/contact', [HomeController::class, 'contactSubmit'])->name('contact.submit');

    // Public Application Routes (No Authentication Required) - Using ProjectApplication system
    Route::prefix('apply')->name('apply.')->group(function () {
        Route::get('/', [ProjectApplicationController::class, 'create'])->name('project');
        Route::post('/', [ProjectApplicationController::class, 'store'])->name('project.store');
        Route::get('/success/{referenceNumber}', [ProjectApplicationController::class, 'success'])->name('project.success');
        Route::post('/status', [ProjectApplicationController::class, 'status'])->name('project.status');
    });

    // Newsletter subscription
    Route::post('/newsletter/subscribe', [App\Http\Controllers\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');
    Route::get('/newsletter/unsubscribe', [App\Http\Controllers\NewsletterController::class, 'showUnsubscribeForm'])->name('newsletter.unsubscribe.form');
    Route::post('/newsletter/unsubscribe', [App\Http\Controllers\NewsletterController::class, 'unsubscribe'])->name('newsletter.unsubscribe');

    // Career Routes (Public Access)
    Route::prefix('careers')->name('careers.')->group(function () {
        Route::get('/', [CareerController::class, 'index'])->name('index');
        Route::get('/job/{job:slug}', [CareerController::class, 'show'])->name('show');
        Route::get('/job/{job:slug}/apply', [CareerController::class, 'apply'])->name('apply');
        Route::post('/job/{job:slug}/apply', [CareerController::class, 'storeApplication'])->name('store-application');
        Route::get('/application-success/{referenceNumber}', [CareerController::class, 'applicationSuccess'])->name('application-success');
        Route::post('/check-status', [CareerController::class, 'checkStatus'])->name('check-status');
    });

    // Shop Routes (with activity logging for error tracking)
    Route::prefix('shop')->name('shop.')->middleware(['activity.log'])->group(function () {
        Route::get('/', [ShopController::class, 'index'])->name('index');
        Route::get('/search', [ShopController::class, 'search'])->name('search');
        Route::get('/category/{category}', [ShopController::class, 'category'])->name('category');
        Route::get('/product/{product}', [ShopController::class, 'product'])->name('product');
        Route::get('/product/{product}/quick-view', [ShopController::class, 'quickView'])->name('product.quick-view');
    });

    // Cart Routes (with activity logging for error tracking)
    Route::prefix('cart')->name('cart.')->middleware(['activity.log'])->group(function () {
        Route::get('/', [CartController::class, 'index'])->name('index');
        Route::post('/add', [CartController::class, 'add'])->name('add');
        Route::patch('/update/{item}', [CartController::class, 'update'])->name('update');
        Route::delete('/remove/{item}', [CartController::class, 'remove'])->name('remove');
        Route::delete('/clear', [CartController::class, 'clear'])->name('clear');
        // Rate limit cart count endpoint to prevent server overload
        Route::get('/count', [CartController::class, 'count'])->name('count')->middleware('throttle:30,1');
        Route::post('/coupon', [CartController::class, 'applyCoupon'])->name('coupon');
        Route::delete('/coupon', [CartController::class, 'removeCoupon'])->name('coupon.remove');
    });

// Checkout Routes (with comprehensive error handling and activity logging)
Route::prefix('checkout')->name('checkout.')->middleware(['checkout.error', 'activity.log'])->group(function () {
    Route::get('/', [CheckoutController::class, 'index'])->name('index');
    Route::post('/process', [CheckoutController::class, 'process'])->name('process');
    Route::get('/payment/{order}', [CheckoutController::class, 'payment'])->name('payment');
    Route::post('/payment/{order}/process', [CheckoutController::class, 'processPayment'])->name('payment.process');
    Route::post('/payment/{order}/complete', [CheckoutController::class, 'paymentComplete'])->name('payment.complete');
    Route::get('/offline-payment/{order}', [CheckoutController::class, 'offlinePayment'])->name('offline-payment');
    Route::get('/success/{order}', [CheckoutController::class, 'success'])->name('success');
    });

    // Services routes
    Route::prefix('services')->name('services.')->group(function () {
        Route::get('/', function () {
            return view('pages.services.index');
        })->name('index');

        // Legacy hardcoded routes (kept for backward compatibility)
        Route::get('/web-development', function () {
            return view('pages.services.web-development');
        })->name('web-development');

        Route::get('/mobile-app-development', function () {
            return view('pages.services.mobile-app-development');
        })->name('mobile-app-development');

        Route::get('/ecommerce-development', function () {
            return view('pages.services.ecommerce-development');
        })->name('ecommerce-development');

        Route::get('/digital-marketing', function () {
            return view('pages.services.digital-marketing');
        })->name('digital-marketing');

        Route::get('/seo-services', function () {
            return view('pages.services.seo-services');
        })->name('seo-services');

        Route::get('/maintenance-support', function () {
            return view('pages.services.maintenance-support');
        })->name('maintenance-support');

        Route::get('/data-analytics', function () {
            return view('pages.services.data-analytics');
        })->name('data-analytics');

        Route::get('/accounting-services', function () {
            return view('pages.services.accounting-services');
        })->name('accounting-services');

        Route::get('/ai-services', function () {
            return view('pages.services.ai-services');
        })->name('ai-services');

        // Dynamic service route (must be last to avoid conflicts with hardcoded routes)
        Route::get('/{service:slug}', [HomeController::class, 'service'])->name('show');
    });

    // Projects routes (public showcase)
    Route::prefix('projects')->name('projects.')->group(function () {
        Route::get('/', [HomeController::class, 'portfolio'])->name('index');
        Route::get('/{project:slug}', [HomeController::class, 'project'])->name('show');
    });

    // Blog routes
    Route::prefix('blog')->name('blog.')->group(function () {
        Route::get('/', [HomeController::class, 'blog'])->name('index');
        Route::get('/featured', [App\Http\Controllers\BlogController::class, 'featured'])->name('featured');
        Route::get('/popular', [App\Http\Controllers\BlogController::class, 'popular'])->name('popular');
        Route::get('/search', [App\Http\Controllers\BlogController::class, 'search'])->name('search');
        Route::get('/archive', [App\Http\Controllers\BlogController::class, 'archive'])->name('archive');
        Route::get('/rss', [App\Http\Controllers\BlogController::class, 'rss'])->name('rss');
        Route::get('/category/{category:slug}', [App\Http\Controllers\BlogController::class, 'category'])->name('category');
        Route::get('/service/{service:slug}', [App\Http\Controllers\BlogController::class, 'service'])->name('service');
        Route::get('/{post:slug}', [HomeController::class, 'blogPost'])->name('show');
    });

    // Comment routes
    Route::prefix('comments')->name('comments.')->group(function () {
        Route::post('/blog/{post:slug}', [App\Http\Controllers\CommentController::class, 'store'])->name('store');
        Route::post('/{comment:uuid}/helpful', [App\Http\Controllers\CommentController::class, 'markHelpful'])->name('helpful');
        Route::delete('/{comment:uuid}/helpful', [App\Http\Controllers\CommentController::class, 'removeHelpful'])->name('remove-helpful');
        Route::post('/{comment:uuid}/flag', [App\Http\Controllers\CommentController::class, 'flag'])->name('flag');
        Route::get('/{comment:uuid}/download/{filename}', [App\Http\Controllers\CommentController::class, 'downloadAttachment'])->name('download');
    });

// Close localized routes group
});

// Non-localized routes (admin, auth, tracking, etc.)

// Admin comment moderation routes - Admin and Staff only with content permissions
Route::prefix('admin/comments')->name('admin.comments.')->middleware(['auth', 'role:admin,staff', 'permission:content,manage'])->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\CommentController::class, 'index'])->name('index');
    Route::get('/{comment:uuid}', [App\Http\Controllers\Admin\CommentController::class, 'show'])->name('show');
    Route::post('/{comment:uuid}/approve', [App\Http\Controllers\Admin\CommentController::class, 'approve'])->name('approve');
    Route::post('/{comment:uuid}/reject', [App\Http\Controllers\Admin\CommentController::class, 'reject'])->name('reject');
    Route::post('/bulk-approve', [App\Http\Controllers\Admin\CommentController::class, 'bulkApprove'])->name('bulk-approve');
    Route::post('/bulk-reject', [App\Http\Controllers\Admin\CommentController::class, 'bulkReject'])->name('bulk-reject');
    Route::get('/stats/data', [App\Http\Controllers\Admin\CommentController::class, 'stats'])->name('stats');
});

// Product review routes
Route::prefix('products/{product:slug}/reviews')->name('product-reviews.')->group(function () {
    Route::post('/', [App\Http\Controllers\ProductReviewController::class, 'store'])->name('store');
    Route::put('/{review:uuid}', [App\Http\Controllers\ProductReviewController::class, 'update'])->name('update');
    Route::get('/eligibility', [App\Http\Controllers\ProductReviewController::class, 'checkEligibility'])->name('eligibility');
});

// Product review interaction routes
Route::prefix('reviews')->name('reviews.')->middleware('auth')->group(function () {
    Route::post('/{review:uuid}/helpful', [App\Http\Controllers\ProductReviewController::class, 'markHelpful'])->name('helpful');
    Route::delete('/{review:uuid}/helpful', [App\Http\Controllers\ProductReviewController::class, 'removeHelpful'])->name('remove-helpful');
    Route::post('/{review:uuid}/flag', [App\Http\Controllers\ProductReviewController::class, 'flag'])->name('flag');
});

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

// Password Reset routes
Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
Route::post('/forgot-password', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
Route::get('/reset-password/{token}', [ForgotPasswordController::class, 'showResetForm'])->name('password.reset');
Route::post('/reset-password', [ForgotPasswordController::class, 'reset'])->name('password.update');

// Legal pages
Route::get('/terms', function () {
    return view('legal.terms');
})->name('terms');

Route::get('/privacy', function () {
    return view('legal.privacy');
})->name('privacy');

// Email verification routes (placeholder)
Route::get('/email/verify/{id}/{hash}', function () {
    return redirect()->route('dashboard')->with('success', 'Email verified successfully!');
})->name('verification.verify')->middleware(['auth', 'signed']);

Route::post('/email/verification-notification', function () {
    return back()->with('message', 'Verification link sent!');
})->name('verification.send')->middleware('auth');



// Admin Dashboard routes (protected with activity logging)
Route::prefix('admin')->name('admin.')->middleware(['activity.log', 'auth', 'role:admin,staff'])->group(function () {
    Route::get('/', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard.alt');
    Route::get('/dashboard/visitor-chart-data', [App\Http\Controllers\Admin\DashboardController::class, 'getVisitorChartData'])->name('dashboard.visitor-chart-data');
    Route::get('/dashboard/top-pages', [App\Http\Controllers\Admin\DashboardController::class, 'getTopPages'])->name('dashboard.top-pages');

    // Chat Moderation routes
    Route::prefix('chat')->name('chat.')->group(function () {
        Route::get('/moderation', [App\Http\Controllers\Admin\ChatModerationController::class, 'index'])->name('moderation.index');
        Route::get('/moderation/stats', [App\Http\Controllers\Admin\ChatModerationController::class, 'getStats'])->name('moderation.stats');
        Route::post('/moderation/block-user', [App\Http\Controllers\Admin\ChatModerationController::class, 'blockUser'])->name('moderation.block-user');
        Route::post('/moderation/unblock-user', [App\Http\Controllers\Admin\ChatModerationController::class, 'unblockUser'])->name('moderation.unblock-user');

        // Chat Room Management routes
        Route::get('/rooms', [App\Http\Controllers\Admin\ChatRoomController::class, 'index'])->name('rooms.index');
        Route::get('/rooms/{chatRoom}', [App\Http\Controllers\Admin\ChatRoomController::class, 'show'])->name('rooms.show');
        Route::post('/rooms/{chatRoom}/messages', [App\Http\Controllers\Admin\ChatRoomController::class, 'sendMessage'])->name('rooms.messages.store');
        Route::post('/rooms/{chatRoom}/assign', [App\Http\Controllers\Admin\ChatRoomController::class, 'assign'])->name('rooms.assign');
        Route::put('/rooms/{chatRoom}', [App\Http\Controllers\Admin\ChatRoomController::class, 'update'])->name('rooms.update');
        Route::post('/rooms/{chatRoom}/close', [App\Http\Controllers\Admin\ChatRoomController::class, 'close'])->name('rooms.close');
        Route::get('/rooms/{chatRoom}/updates', [App\Http\Controllers\Admin\ChatRoomController::class, 'getUpdates'])->name('rooms.updates');
        Route::post('/moderation/delete-message', [App\Http\Controllers\Admin\ChatModerationController::class, 'deleteMessage'])->name('moderation.delete-message');
        Route::post('/moderation/escalate', [App\Http\Controllers\Admin\ChatModerationController::class, 'escalateChat'])->name('moderation.escalate');

        // AI Configuration routes
        Route::get('/ai', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'index'])->name('ai.index');
        Route::post('/ai/settings', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'updateSettings'])->name('ai.settings.update');
        Route::post('/ai/templates', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'updateTemplates'])->name('ai.templates.update');
        Route::post('/ai/test', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'testConnection'])->name('ai.test');
        Route::get('/ai/analytics', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'getAnalytics'])->name('ai.analytics');
        Route::get('/ai/enhanced-analytics', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'getEnhancedAnalytics'])->name('ai.enhanced-analytics');

        // AI Provider Management
        Route::post('/ai/test-provider', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'testProvider'])->name('ai.test-provider');
        Route::get('/ai/providers', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'getProviders'])->name('ai.providers');
        Route::get('/ai/provider-status', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'getProviderStatus'])->name('ai.provider-status');
        Route::get('/ai/usage-stats', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'getUsageStats'])->name('ai.usage-stats');
        Route::post('/ai/provider-settings', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'updateProviderSettings'])->name('ai.provider-settings.update');
        Route::get('/ai/analytics-dashboard', [App\Http\Controllers\Admin\ChatAIConfigController::class, 'analyticsDashboard'])->name('ai.analytics-dashboard');

        // Training Data Management
        Route::prefix('training-data')->name('training-data.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\TrainingDataController::class, 'index'])->name('index');
            Route::get('/data', [App\Http\Controllers\Admin\TrainingDataController::class, 'getData'])->name('data');
            Route::post('/', [App\Http\Controllers\Admin\TrainingDataController::class, 'store'])->name('store');
            Route::get('/{trainingData}', [App\Http\Controllers\Admin\TrainingDataController::class, 'show'])->name('show');
            Route::put('/{trainingData}', [App\Http\Controllers\Admin\TrainingDataController::class, 'update'])->name('update');
            Route::delete('/{trainingData}', [App\Http\Controllers\Admin\TrainingDataController::class, 'destroy'])->name('destroy');
            Route::post('/bulk', [App\Http\Controllers\Admin\TrainingDataController::class, 'bulkAction'])->name('bulk');
            Route::post('/import', [App\Http\Controllers\Admin\TrainingDataController::class, 'import'])->name('import');
            Route::post('/export', [App\Http\Controllers\Admin\TrainingDataController::class, 'export'])->name('export');
            Route::get('/download/{filename}', [App\Http\Controllers\Admin\TrainingDataController::class, 'download'])->name('download');
        });

        // Chat Analytics Routes
        Route::prefix('analytics')->name('analytics.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'index'])->name('index');
            Route::get('/metrics/realtime', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getRealTimeMetrics'])->name('metrics.realtime');
            Route::get('/metrics/ai', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getAIMetrics'])->name('metrics.ai');
            Route::get('/trends', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getTrends'])->name('trends');
            Route::get('/staff', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getStaffPerformance'])->name('staff');
            Route::get('/satisfaction', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getCustomerSatisfaction'])->name('satisfaction');
            Route::get('/summary', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getSummary'])->name('summary');
            Route::get('/historical', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getHistoricalData'])->name('historical');
            Route::get('/peak-usage', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'getPeakUsage'])->name('peak.usage');
            Route::post('/export', [App\Http\Controllers\Admin\ChatAnalyticsController::class, 'exportReport'])->name('export');
        });

        // AI Performance Routes (RESTful with versioning)
        Route::prefix('ai-performance')->name('ai.performance.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\AIPerformanceController::class, 'index'])->name('index');

            // RESTful API routes with versioning
            Route::prefix('api/v1')->name('api.v1.')->group(function () {
                Route::get('/reports', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getReport'])->name('reports.index');
                Route::get('/reports/summary', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getSummary'])->name('reports.summary');
                Route::get('/metrics/success', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getSuccessMetrics'])->name('metrics.success');
                Route::get('/metrics/escalation', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getEscalationAnalysis'])->name('metrics.escalation');
                Route::get('/metrics/response-time', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getResponseTimeAnalysis'])->name('metrics.response_time');
                Route::get('/metrics/confidence', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getConfidenceAnalysis'])->name('metrics.confidence');
                Route::get('/performance/languages', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getLanguagePerformance'])->name('performance.languages');
                Route::get('/performance/models', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getModelComparison'])->name('performance.models');
                Route::get('/analytics/topics', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getTrendingTopics'])->name('analytics.topics');
                Route::get('/analytics/failures', [App\Http\Controllers\Admin\AIPerformanceController::class, 'getFailureAnalysis'])->name('analytics.failures');
                Route::post('/reports/export', [App\Http\Controllers\Admin\AIPerformanceController::class, 'exportReport'])->name('reports.export');
            });
        });

        // Customer Satisfaction Routes (RESTful with versioning)
        Route::prefix('satisfaction')->name('satisfaction.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'index'])->name('index');

            // RESTful API routes with versioning
            Route::prefix('api/v1')->name('api.v1.')->group(function () {
                Route::get('/metrics', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getMetrics'])->name('metrics.index');
                Route::get('/metrics/overview', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getOverview'])->name('metrics.overview');
                Route::get('/metrics/ratings', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getRatingsBreakdown'])->name('metrics.ratings');
                Route::get('/metrics/trends', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getTrends'])->name('metrics.trends');
                Route::get('/analytics/feedback', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getFeedbackAnalysis'])->name('analytics.feedback');
                Route::get('/performance/staff', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getStaffPerformance'])->name('performance.staff');
                Route::get('/metrics/nps', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getNPSMetrics'])->name('metrics.nps');
                Route::get('/survey/questions', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'getSurveyQuestions'])->name('survey.questions');
                Route::post('/ratings', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'submitRating'])->name('ratings.store');
                Route::post('/reports/export', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'exportReport'])->name('reports.export');
            });
        });

        // Conversation Insights Dashboard
        Route::prefix('insights')->name('insights.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'index'])->name('index');

            // RESTful API routes with versioning
            Route::prefix('api/v1')->name('api.v1.')->group(function () {
                Route::get('/insights', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getInsights'])->name('insights.index');
                Route::get('/overview', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getOverview'])->name('overview');
                Route::get('/patterns', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getPatterns'])->name('patterns');
                Route::get('/trending-topics', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getTrendingTopics'])->name('trending.topics');
                Route::get('/usage-patterns', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getUsagePatterns'])->name('usage.patterns');
                Route::get('/conversation-flow', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getConversationFlow'])->name('conversation.flow');
                Route::get('/peak-hours', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getPeakHours'])->name('peak.hours');
                Route::get('/user-behavior', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getUserBehavior'])->name('user.behavior');
                Route::get('/staff-insights', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'getStaffInsights'])->name('staff.insights');
                Route::post('/export', [App\Http\Controllers\Admin\ConversationInsightsController::class, 'exportReport'])->name('export');
            });
        });

        // Chat Webhooks Management
        Route::prefix('chat/webhooks')->name('chat.webhooks.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ChatWebhookController::class, 'index'])->name('index');
            Route::get('/create', [App\Http\Controllers\Admin\ChatWebhookController::class, 'create'])->name('create');
            Route::post('/', [App\Http\Controllers\Admin\ChatWebhookController::class, 'store'])->name('store');
            Route::get('/{webhook}', [App\Http\Controllers\Admin\ChatWebhookController::class, 'show'])->name('show');
            Route::get('/{webhook}/edit', [App\Http\Controllers\Admin\ChatWebhookController::class, 'edit'])->name('edit');
            Route::put('/{webhook}', [App\Http\Controllers\Admin\ChatWebhookController::class, 'update'])->name('update');
            Route::delete('/{webhook}', [App\Http\Controllers\Admin\ChatWebhookController::class, 'destroy'])->name('destroy');
            Route::post('/{webhook}/test', [App\Http\Controllers\Admin\ChatWebhookController::class, 'test'])->name('test');
            Route::post('/{webhook}/toggle', [App\Http\Controllers\Admin\ChatWebhookController::class, 'toggle'])->name('toggle');
            Route::get('/{webhook}/deliveries', [App\Http\Controllers\Admin\ChatWebhookController::class, 'deliveries'])->name('deliveries');
            Route::post('/{webhook}/retry', [App\Http\Controllers\Admin\ChatWebhookController::class, 'retryDeliveries'])->name('retry');
        });

        // Chat Search & Advanced Features
        Route::prefix('chat/search')->name('chat.search.')->group(function () {
            Route::get('/', [App\Http\Controllers\Admin\ChatSearchController::class, 'index'])->name('index');
            Route::post('/messages', [App\Http\Controllers\Admin\ChatSearchController::class, 'searchMessages'])->name('messages');
            Route::post('/rooms', [App\Http\Controllers\Admin\ChatSearchController::class, 'searchRooms'])->name('rooms');
            Route::post('/global', [App\Http\Controllers\Admin\ChatSearchController::class, 'globalSearch'])->name('global');
            Route::post('/participant', [App\Http\Controllers\Admin\ChatSearchController::class, 'searchByParticipant'])->name('participant');
            Route::post('/ai-messages', [App\Http\Controllers\Admin\ChatSearchController::class, 'searchAIMessages'])->name('ai-messages');
            Route::get('/suggestions', [App\Http\Controllers\Admin\ChatSearchController::class, 'getSuggestions'])->name('suggestions');
            Route::get('/analytics', [App\Http\Controllers\Admin\ChatSearchController::class, 'getAnalytics'])->name('analytics');
            Route::post('/export', [App\Http\Controllers\Admin\ChatSearchController::class, 'exportResults'])->name('export');
            Route::post('/advanced', [App\Http\Controllers\Admin\ChatSearchController::class, 'advancedSearch'])->name('advanced');
        });
    });

    // Customer Satisfaction - Public routes for authenticated users
    Route::middleware(['auth'])->prefix('chat/satisfaction')->name('chat.satisfaction.')->group(function () {
        Route::post('/ratings', [App\Http\Controllers\Admin\CustomerSatisfactionController::class, 'submitRating'])->name('ratings.store');
    });

    // AI Provider Management - Admin only
    Route::middleware(['auth', 'role:admin'])->prefix('admin/ai-providers')->name('admin.ai-providers.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\AIProviderController::class, 'index'])->name('index');
        Route::get('/providers', [App\Http\Controllers\Admin\AIProviderController::class, 'getProviders'])->name('providers');
        Route::get('/providers/{provider}/models', [App\Http\Controllers\Admin\AIProviderController::class, 'getModels'])->name('models');
        Route::get('/models/tier/{tier}', [App\Http\Controllers\Admin\AIProviderController::class, 'getModelsByTier'])->name('models.tier');
        Route::post('/test', [App\Http\Controllers\Admin\AIProviderController::class, 'testProvider'])->name('test');
        Route::post('/cost-estimate', [App\Http\Controllers\Admin\AIProviderController::class, 'getCostEstimate'])->name('cost-estimate');
        Route::get('/usage-stats', [App\Http\Controllers\Admin\AIProviderController::class, 'getUsageStats'])->name('usage-stats');
        Route::post('/reset-stats', [App\Http\Controllers\Admin\AIProviderController::class, 'resetUsageStats'])->name('reset-stats');
        Route::post('/test-sentiment', [App\Http\Controllers\Admin\AIProviderController::class, 'testSentiment'])->name('test-sentiment');
        Route::post('/test-translation', [App\Http\Controllers\Admin\AIProviderController::class, 'testTranslation'])->name('test-translation');
        Route::post('/compare', [App\Http\Controllers\Admin\AIProviderController::class, 'compareProviders'])->name('compare');
    });

// Health Check Routes (no authentication required for monitoring)
Route::prefix('health')->group(function () {
    Route::get('/', [App\Http\Controllers\HealthController::class, 'health']);
    Route::get('/detailed', [App\Http\Controllers\HealthController::class, 'detailed']);
    Route::get('/ready', [App\Http\Controllers\HealthController::class, 'ready']);
    Route::get('/live', [App\Http\Controllers\HealthController::class, 'live']);
    Route::get('/metrics', [App\Http\Controllers\HealthController::class, 'metrics']);
    Route::get('/prometheus', [App\Http\Controllers\HealthController::class, 'prometheus']);
});

    // Global Search
    Route::get('/search', [App\Http\Controllers\Admin\SearchController::class, 'search'])->name('search');
    Route::get('/search/advanced', [App\Http\Controllers\Admin\SearchController::class, 'advanced'])->name('search.advanced');
    Route::get('/search/suggestions', [App\Http\Controllers\Admin\SearchController::class, 'suggestions'])->name('search.suggestions');
    Route::get('/search/filters', [App\Http\Controllers\Admin\SearchController::class, 'filters'])->name('search.filters');

    // Categories Management
    Route::resource('categories', App\Http\Controllers\Admin\CategoryController::class);
    Route::post('/categories/reorder', [App\Http\Controllers\Admin\CategoryController::class, 'reorder'])->name('categories.reorder');
    Route::get('/categories-api', [App\Http\Controllers\Admin\CategoryController::class, 'getCategories'])->name('categories.api');
    Route::post('/categories/{category}/toggle-featured', [App\Http\Controllers\Admin\CategoryController::class, 'toggleFeatured'])->name('categories.toggle-featured');

    // Products Management
    Route::resource('products', App\Http\Controllers\Admin\ProductController::class);
    Route::post('/products/{product}/images', [App\Http\Controllers\Admin\ProductController::class, 'uploadImages'])->name('products.images.upload');
    Route::delete('/products/{product}/images/{image}', [App\Http\Controllers\Admin\ProductController::class, 'deleteImage'])->name('products.images.delete');
    Route::post('/products/{product}/toggle-featured', [App\Http\Controllers\Admin\ProductController::class, 'toggleFeatured'])->name('products.toggle-featured');

    // Coupons Management
    Route::resource('coupons', App\Http\Controllers\Admin\CouponController::class);
    Route::post('/coupons/{coupon}/toggle', [App\Http\Controllers\Admin\CouponController::class, 'toggle'])->name('coupons.toggle');

    // Orders Management
    Route::resource('orders', App\Http\Controllers\Admin\OrderController::class)->only(['index', 'show', 'update']);
    Route::patch('/orders/{order}/status', [App\Http\Controllers\Admin\OrderController::class, 'updateStatus'])->name('orders.status');

    // Users Management
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::post('users/{user}/toggle', [App\Http\Controllers\Admin\UserController::class, 'toggle'])->name('users.toggle');
    Route::post('users/{user}/verify-email', [App\Http\Controllers\Admin\UserController::class, 'verifyEmail'])->name('users.verify-email');
    Route::post('users/{user}/resend-verification', [App\Http\Controllers\Admin\UserController::class, 'resendVerification'])->name('users.resend-verification');

    // Projects Management
    Route::resource('projects', App\Http\Controllers\Admin\ProjectController::class);
    Route::post('/projects/{project:uuid}/toggle-published', [App\Http\Controllers\Admin\ProjectController::class, 'togglePublished'])->name('projects.toggle-published');
    Route::post('/projects/{project:uuid}/toggle-featured', [App\Http\Controllers\Admin\ProjectController::class, 'toggleFeatured'])->name('projects.toggle-featured');

    // Project Applications Management
    Route::resource('project-applications', App\Http\Controllers\Admin\ProjectApplicationController::class);
    Route::patch('/project-applications/{projectApplication}/status', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'updateStatus'])->name('project-applications.update-status');
    Route::get('/project-applications/{projectApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'downloadAttachment'])->name('project-applications.download');
    Route::get('/project-applications-statistics', [App\Http\Controllers\Admin\ProjectApplicationController::class, 'getStatistics'])->name('project-applications.statistics');

    // Jobs Management
    Route::resource('jobs', App\Http\Controllers\Admin\JobController::class);
    Route::post('/jobs/{job}/toggle-featured', [App\Http\Controllers\Admin\JobController::class, 'toggleFeatured'])->name('jobs.toggle-featured');
    Route::post('/jobs/{job}/toggle-active', [App\Http\Controllers\Admin\JobController::class, 'toggleActive'])->name('jobs.toggle-active');

    // Contact Submissions Management
    Route::resource('contact-submissions', App\Http\Controllers\Admin\ContactSubmissionController::class)->only(['index', 'show', 'destroy']);
    Route::post('/contact-submissions/{contactSubmission:uuid}/mark-read', [App\Http\Controllers\Admin\ContactSubmissionController::class, 'markAsRead'])->name('contact-submissions.mark-read');
    Route::post('/contact-submissions/{contactSubmission:uuid}/mark-spam', [App\Http\Controllers\Admin\ContactSubmissionController::class, 'markAsSpam'])->name('contact-submissions.mark-spam');
    Route::post('/contact-submissions/{contactSubmission:uuid}/reply', [App\Http\Controllers\Admin\ContactSubmissionController::class, 'reply'])->name('contact-submissions.reply');

    // Newsletter Subscriptions Management
    Route::resource('newsletter-subscriptions', App\Http\Controllers\Admin\NewsletterSubscriptionController::class)->only(['index', 'show', 'destroy']);
    Route::post('/newsletter-subscriptions/{newsletterSubscription:uuid}/toggle-status', [App\Http\Controllers\Admin\NewsletterSubscriptionController::class, 'toggleStatus'])->name('newsletter-subscriptions.toggle-status');
    Route::get('/newsletter-subscriptions/export', [App\Http\Controllers\Admin\NewsletterSubscriptionController::class, 'export'])->name('newsletter-subscriptions.export');

    // Visitor Analytics Management
    Route::get('/visitor-analytics/chart-data', [App\Http\Controllers\Admin\VisitorAnalyticsController::class, 'getChartData'])->name('visitor-analytics.chart-data');
    Route::get('/visitor-analytics/export', [App\Http\Controllers\Admin\VisitorAnalyticsController::class, 'export'])->name('visitor-analytics.export');
    Route::resource('visitor-analytics', App\Http\Controllers\Admin\VisitorAnalyticsController::class)->only(['index', 'show']);

    // Job Applications Management
    Route::resource('job-applications', App\Http\Controllers\Admin\JobApplicationController::class)->only(['index', 'show', 'update', 'destroy']);
    Route::patch('/job-applications/{jobApplication}/status', [App\Http\Controllers\Admin\JobApplicationController::class, 'updateStatus'])->name('job-applications.update-status');
    Route::get('/job-applications/{jobApplication}/download/{attachmentIndex}', [App\Http\Controllers\Admin\JobApplicationController::class, 'downloadAttachment'])->name('job-applications.download');
    Route::get('/job-applications-statistics', [App\Http\Controllers\Admin\JobApplicationController::class, 'getStatistics'])->name('job-applications.statistics');



    // Activity Logs - Admin and Staff only
    Route::get('/activity-logs', [App\Http\Controllers\Admin\ActivityLogController::class, 'index'])->name('activity-logs.index');
    Route::get('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'show'])->name('activity-logs.show');
    Route::delete('/activity-logs/{activityLog}', [App\Http\Controllers\Admin\ActivityLogController::class, 'destroy'])->name('activity-logs.destroy');
    Route::get('/activity-logs-data', [App\Http\Controllers\Admin\ActivityLogController::class, 'data'])->name('activity-logs.data');
    Route::delete('/activity-logs/bulk-delete', [App\Http\Controllers\Admin\ActivityLogController::class, 'bulkDelete'])->name('activity-logs.bulk-delete');
    Route::delete('/activity-logs/clean-old', [App\Http\Controllers\Admin\ActivityLogController::class, 'cleanOldLogs'])->name('activity-logs.clean-old');
    Route::get('/activity-logs/export', [App\Http\Controllers\Admin\ActivityLogController::class, 'export'])->name('activity-logs.export');
    Route::get('/activity-logs-stats', [App\Http\Controllers\Admin\ActivityLogController::class, 'stats'])->name('activity-logs.stats');

    // Email Marketing Management
    Route::resource('email-templates', App\Http\Controllers\Admin\EmailTemplateController::class);
    Route::post('/email-templates/{emailTemplate}/duplicate', [App\Http\Controllers\Admin\EmailTemplateController::class, 'duplicate'])->name('email-templates.duplicate');
    Route::get('/email-templates/{emailTemplate}/preview', [App\Http\Controllers\Admin\EmailTemplateController::class, 'preview'])->name('email-templates.preview');
    Route::post('/email-templates/{emailTemplate}/toggle-status', [App\Http\Controllers\Admin\EmailTemplateController::class, 'toggleStatus'])->name('email-templates.toggle-status');
    Route::post('/email-templates/{emailTemplate}/set-default', [App\Http\Controllers\Admin\EmailTemplateController::class, 'setDefault'])->name('email-templates.set-default');
    Route::get('/email-templates/{emailTemplate}/variables', [App\Http\Controllers\Admin\EmailTemplateController::class, 'getVariables'])->name('email-templates.variables');
    Route::post('/email-templates/{emailTemplate}/upload-preview', [App\Http\Controllers\Admin\EmailTemplateController::class, 'uploadPreview'])->name('email-templates.upload-preview');
    Route::post('/email-templates/bulk-action', [App\Http\Controllers\Admin\EmailTemplateController::class, 'bulkAction'])->name('email-templates.bulk-action');

    // Email Campaigns Management
    Route::resource('email-campaigns', App\Http\Controllers\Admin\EmailCampaignController::class);
    Route::post('/email-campaigns/{emailCampaign}/schedule', [App\Http\Controllers\Admin\EmailCampaignController::class, 'schedule'])->name('email-campaigns.schedule');
    Route::post('/email-campaigns/{emailCampaign}/send-now', [App\Http\Controllers\Admin\EmailCampaignController::class, 'sendNow'])->name('email-campaigns.send-now');
    Route::post('/email-campaigns/{emailCampaign}/pause', [App\Http\Controllers\Admin\EmailCampaignController::class, 'pause'])->name('email-campaigns.pause');
    Route::post('/email-campaigns/{emailCampaign}/resume', [App\Http\Controllers\Admin\EmailCampaignController::class, 'resume'])->name('email-campaigns.resume');
    Route::post('/email-campaigns/{emailCampaign}/cancel', [App\Http\Controllers\Admin\EmailCampaignController::class, 'cancel'])->name('email-campaigns.cancel');
    Route::post('/email-campaigns/{emailCampaign}/duplicate', [App\Http\Controllers\Admin\EmailCampaignController::class, 'duplicate'])->name('email-campaigns.duplicate');
    Route::get('/email-campaigns/{emailCampaign}/analytics', [App\Http\Controllers\Admin\EmailCampaignController::class, 'analytics'])->name('email-campaigns.analytics');
    Route::get('/email-campaigns/{emailCampaign}/preview', [App\Http\Controllers\Admin\EmailCampaignController::class, 'preview'])->name('email-campaigns.preview');
    Route::post('/email-campaigns/get-targeted-subscribers', [App\Http\Controllers\Admin\EmailCampaignController::class, 'getTargetedSubscribers'])->name('email-campaigns.get-targeted-subscribers');
    Route::get('/email-campaigns/{emailCampaign}/create-drip-email', [App\Http\Controllers\Admin\EmailCampaignController::class, 'createDripEmail'])->name('email-campaigns.create-drip-email');
    Route::post('/email-campaigns/bulk-action', [App\Http\Controllers\Admin\EmailCampaignController::class, 'bulkAction'])->name('email-campaigns.bulk-action');

    // Email Analytics Management
    Route::get('/email-analytics', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'index'])->name('email-analytics.index');
    Route::get('/email-analytics/campaign-performance', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'getCampaignPerformance'])->name('email-analytics.campaign-performance');
    Route::get('/email-analytics/engagement-metrics', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'getEngagementMetrics'])->name('email-analytics.engagement-metrics');
    Route::get('/email-analytics/subscriber-engagement', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'getSubscriberEngagement'])->name('email-analytics.subscriber-engagement');
    Route::get('/email-analytics/deliverability-metrics', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'getDeliverabilityMetrics'])->name('email-analytics.deliverability-metrics');
    Route::get('/email-analytics/campaign-comparison', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'getCampaignComparison'])->name('email-analytics.campaign-comparison');
    Route::get('/email-analytics/export', [App\Http\Controllers\Admin\EmailAnalyticsController::class, 'export'])->name('email-analytics.export');

    // Subscriber Tags Management
    Route::resource('subscriber-tags', App\Http\Controllers\Admin\SubscriberTagController::class);
    Route::post('/subscriber-tags/{subscriberTag}/toggle-status', [App\Http\Controllers\Admin\SubscriberTagController::class, 'toggleStatus'])->name('subscriber-tags.toggle-status');
    Route::post('/subscriber-tags/{subscriberTag}/assign-subscribers', [App\Http\Controllers\Admin\SubscriberTagController::class, 'assignToSubscribers'])->name('subscriber-tags.assign-subscribers');
    Route::post('/subscriber-tags/{subscriberTag}/remove-subscribers', [App\Http\Controllers\Admin\SubscriberTagController::class, 'removeFromSubscribers'])->name('subscriber-tags.remove-subscribers');
    Route::get('/subscriber-tags/{subscriberTag}/available-subscribers', [App\Http\Controllers\Admin\SubscriberTagController::class, 'getAvailableSubscribers'])->name('subscriber-tags.available-subscribers');
    Route::get('/subscriber-tags/{subscriberTag}/statistics', [App\Http\Controllers\Admin\SubscriberTagController::class, 'getStatistics'])->name('subscriber-tags.statistics');
    Route::post('/subscriber-tags/bulk-action', [App\Http\Controllers\Admin\SubscriberTagController::class, 'bulkAction'])->name('subscriber-tags.bulk-action');

    // Subscriber Segmentation Management
    Route::get('/subscriber-segments', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'index'])->name('subscriber-segments.index');
    Route::get('/subscriber-segments/subscribers', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'getSegmentedSubscribers'])->name('subscriber-segments.subscribers');
    Route::post('/subscriber-segments/preview', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'previewSegment'])->name('subscriber-segments.preview');
    Route::get('/subscriber-segments/export', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'exportSegment'])->name('subscriber-segments.export');
    Route::post('/subscriber-segments/bulk-tag-assignment', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'bulkTagAssignment'])->name('subscriber-segments.bulk-tag-assignment');
    Route::get('/subscriber-segments/lifecycle-analytics', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'getLifecycleAnalytics'])->name('subscriber-segments.lifecycle-analytics');
    Route::get('/subscriber-segments/engagement-analytics', [App\Http\Controllers\Admin\SubscriberSegmentController::class, 'getEngagementAnalytics'])->name('subscriber-segments.engagement-analytics');

    // User Preferences
    Route::prefix('preferences')->name('preferences.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\UserPreferencesController::class, 'index'])->name('index');
        Route::post('/update', [App\Http\Controllers\Admin\UserPreferencesController::class, 'update'])->name('update');
        Route::post('/update-single', [App\Http\Controllers\Admin\UserPreferencesController::class, 'updateSingle'])->name('update-single');
        Route::post('/reset', [App\Http\Controllers\Admin\UserPreferencesController::class, 'reset'])->name('reset');
        Route::get('/export', [App\Http\Controllers\Admin\UserPreferencesController::class, 'export'])->name('export');
    });
});

// Customer Dashboard routes (protected with activity logging)
Route::middleware(['activity.log', 'auth'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/search', [App\Http\Controllers\DashboardController::class, 'search'])->name('dashboard.search');

    // Orders
    Route::get('/orders', [App\Http\Controllers\OrderController::class, 'index'])->name('orders.index');
    Route::get('/orders/{order}', [App\Http\Controllers\OrderController::class, 'show'])->name('orders.show');
    Route::delete('/orders/{order}', [App\Http\Controllers\OrderController::class, 'destroy'])->name('orders.destroy');

    // My Projects (for all authenticated users)
    Route::get('/my-projects', [App\Http\Controllers\ProjectController::class, 'index'])->name('my-projects.index');
    Route::get('/my-projects/{project}', [App\Http\Controllers\ProjectController::class, 'show'])->name('my-projects.show');
    Route::delete('/my-projects/{project}', [App\Http\Controllers\ProjectController::class, 'destroy'])->name('my-projects.destroy');

    // Project Applications (Unified system for both public and authenticated users)
    Route::resource('project-applications', ProjectApplicationController::class);

    // Career Applications (for authenticated users)
    Route::get('/my-job-applications', [CareerController::class, 'myApplications'])->name('careers.my-applications');

    // Address Management
    Route::get('/addresses', [App\Http\Controllers\AddressController::class, 'index'])->name('addresses.index');
    Route::get('/addresses/create', [App\Http\Controllers\AddressController::class, 'create'])->name('addresses.create');
    Route::post('/addresses', [App\Http\Controllers\AddressController::class, 'store'])->name('addresses.store');
    Route::get('/addresses/{address}/edit', [App\Http\Controllers\AddressController::class, 'edit'])->name('addresses.edit');
    Route::patch('/addresses/{address}', [App\Http\Controllers\AddressController::class, 'update'])->name('addresses.update');
    Route::delete('/addresses/{address}', [App\Http\Controllers\AddressController::class, 'destroy'])->name('addresses.destroy');
    Route::patch('/addresses/{address}/set-default', [App\Http\Controllers\AddressController::class, 'setDefault'])->name('addresses.set-default');

    // Profile Management
    Route::get('/profile/edit', [App\Http\Controllers\ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [App\Http\Controllers\ProfileController::class, 'update'])->name('profile.update');
    Route::patch('/profile/password', [App\Http\Controllers\ProfileController::class, 'updatePassword'])->name('profile.password.update');
    Route::delete('/profile/avatar', [App\Http\Controllers\ProfileController::class, 'deleteAvatar'])->name('profile.avatar.delete');
    Route::get('/profile/delete-account', [App\Http\Controllers\ProfileController::class, 'deleteAccount'])->name('profile.delete-account');
    Route::delete('/profile', [App\Http\Controllers\ProfileController::class, 'destroyAccount'])->name('profile.destroy');

    /*
    // Login History Management
    Route::prefix('login-history')->name('login-history.')->group(function () {
        Route::get('/', [App\Http\Controllers\LoginHistoryController::class, 'index'])->name('index');
        Route::get('/data', [App\Http\Controllers\LoginHistoryController::class, 'data'])->name('data');
        Route::get('/stats', [App\Http\Controllers\LoginHistoryController::class, 'stats'])->name('stats');
        Route::delete('/{loginHistory:public_id}', [App\Http\Controllers\LoginHistoryController::class, 'destroy'])->name('destroy');
    });
    */

    
    // Client-specific routes will be added here
});

// Webhook routes (exclude from CSRF protection - outside localization)
Route::post('/webhooks/stripe', [WebhookController::class, 'stripe'])->name('webhooks.stripe');
Route::post('/webhooks/paypal', [WebhookController::class, 'paypal'])->name('webhooks.paypal');

// Test route for visitor analytics (remove in production)
Route::get('/test-visitor-analytics', function () {
    $analytics = app(\App\Services\VisitorAnalytics::class);
    $visit = $analytics->trackPageVisit('Test Page', ['test' => true]);

    return response()->json([
        'message' => 'Visitor analytics test successful',
        'visit_id' => $visit->uuid,
        'visitor_id' => $visit->visitor_id,
        'is_returning' => $visit->is_returning_visitor,
        'device_type' => $visit->device_type,
        'browser' => $visit->browser,
        'ip_address' => $visit->ip_address,
    ]);
})->name('test.visitor.analytics');

// Test route for newsletter functionality (remove in production)
Route::get('/test-newsletter', function () {
    return view('test.newsletter');
})->name('test.newsletter');

// Email Tracking Routes (Public - no authentication required)
Route::get('/email/track/open/{token}', [App\Http\Controllers\EmailTrackingController::class, 'trackOpen'])->name('email.track.open');
Route::get('/email/track/click/{token}', [App\Http\Controllers\EmailTrackingController::class, 'trackClick'])->name('email.track.click');
Route::match(['GET', 'POST'], '/email/unsubscribe/{token}', [App\Http\Controllers\EmailTrackingController::class, 'unsubscribe'])->name('email.unsubscribe');
Route::get('/unsubscribe/success', [App\Http\Controllers\EmailTrackingController::class, 'unsubscribeSuccess'])->name('unsubscribe.success');

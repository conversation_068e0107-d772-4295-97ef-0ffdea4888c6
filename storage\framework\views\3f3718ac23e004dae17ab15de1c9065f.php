<?php $__env->startSection('title', $job->title . ' - Careers'); ?>
<?php $__env->startSection('meta_description', Str::limit(strip_tags($job->description), 160)); ?>

<?php $__env->startSection('content'); ?>
<!-- Job Header -->
<section class="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="flex items-start justify-between mb-6">
                <div class="flex-1">
                    <h1 class="text-3xl lg:text-4xl font-bold mb-4"><?php echo e($job->title); ?></h1>
                    <div class="flex flex-wrap gap-4 text-blue-100">
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <?php echo e($job->department); ?>

                        </span>
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                            </svg>
                            <?php echo e($job->location); ?>

                            <?php if($job->is_remote): ?>
                                <span class="ml-1 text-green-300">(Remote)</span>
                            <?php endif; ?>
                        </span>
                        <span class="flex items-center">
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                            </svg>
                            <?php echo e(ucfirst(str_replace('-', ' ', $job->employment_type))); ?>

                        </span>
                    </div>
                </div>
                <?php if($job->is_featured): ?>
                    <span class="bg-yellow-400 text-yellow-900 text-sm font-medium px-3 py-1 rounded-full">Featured</span>
                <?php endif; ?>
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
                <a href="<?php echo e(route('careers.apply', ['locale' => app()->getLocale(), 'job' => $job])); ?>"
                   class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors text-center">
                    Apply Now
                </a>
                <a href="<?php echo e(route('careers.index', ['locale' => app()->getLocale()])); ?>"
                   class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors text-center">
                    Back to Jobs
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Job Details -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Job Description -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Job Description</h2>
                        <div class="prose max-w-none text-gray-700">
                            <?php echo nl2br(e($job->description)); ?>

                        </div>
                    </div>

                    <!-- Responsibilities -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Key Responsibilities</h2>
                        <div class="prose max-w-none text-gray-700">
                            <?php echo nl2br(e($job->responsibilities)); ?>

                        </div>
                    </div>

                    <!-- Requirements -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Requirements</h2>
                        <div class="prose max-w-none text-gray-700">
                            <?php echo nl2br(e($job->requirements)); ?>

                        </div>
                    </div>

                    <?php if($job->benefits): ?>
                    <!-- Benefits -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Benefits & Perks</h2>
                        <div class="prose max-w-none text-gray-700">
                            <?php echo nl2br(e($job->benefits)); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Job Summary -->
                    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Job Summary</h3>
                        <div class="space-y-3">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Employment Type</span>
                                <p class="text-gray-900"><?php echo e(ucfirst(str_replace('-', ' ', $job->employment_type))); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Experience Level</span>
                                <p class="text-gray-900"><?php echo e(ucfirst(str_replace('-', ' ', $job->experience_level))); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Department</span>
                                <p class="text-gray-900"><?php echo e($job->department); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Location</span>
                                <p class="text-gray-900">
                                    <?php echo e($job->location); ?>

                                    <?php if($job->is_remote): ?>
                                        <span class="text-green-600">(Remote Available)</span>
                                    <?php endif; ?>
                                </p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Salary</span>
                                <p class="text-gray-900 font-semibold text-green-600"><?php echo e($job->formatted_salary); ?></p>
                            </div>
                            <?php if($job->application_deadline): ?>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Application Deadline</span>
                                <p class="text-gray-900"><?php echo e($job->application_deadline->format('M j, Y')); ?></p>
                            </div>
                            <?php endif; ?>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Posted</span>
                                <p class="text-gray-900"><?php echo e($job->created_at->format('M j, Y')); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Apply Button -->
                    <div class="bg-blue-50 rounded-lg p-6 mb-8">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Ready to Apply?</h3>
                        <p class="text-gray-600 mb-4">Join our team and help us build amazing products!</p>
                        <a href="<?php echo e(route('careers.apply', ['locale' => app()->getLocale(), 'job' => $job])); ?>"
                           class="block w-full bg-blue-600 text-white text-center py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            Apply for This Position
                        </a>
                    </div>

                    <!-- Share -->
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Share This Job</h3>
                        <div class="flex space-x-3">
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo e(urlencode(request()->fullUrl())); ?>" 
                               target="_blank" 
                               class="flex-1 bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 transition-colors">
                                LinkedIn
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo e(urlencode(request()->fullUrl())); ?>&text=<?php echo e(urlencode('Check out this job opportunity: ' . $job->title)); ?>" 
                               target="_blank" 
                               class="flex-1 bg-blue-400 text-white text-center py-2 rounded-md hover:bg-blue-500 transition-colors">
                                Twitter
                            </a>
                            <button onclick="copyToClipboard('<?php echo e(request()->fullUrl()); ?>')" 
                                    class="flex-1 bg-gray-600 text-white text-center py-2 rounded-md hover:bg-gray-700 transition-colors">
                                Copy Link
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php if($relatedJobs->count() > 0): ?>
<!-- Related Jobs -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Other Open Positions</h2>
            <div class="grid gap-6">
                <?php $__currentLoopData = $relatedJobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedJob): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <a href="<?php echo e(route('careers.show', ['locale' => app()->getLocale(), 'job' => $relatedJob])); ?>" class="hover:text-blue-600 transition-colors">
                                        <?php echo e($relatedJob->title); ?>

                                    </a>
                                </h3>
                                <div class="flex items-center space-x-4 text-sm text-gray-600">
                                    <span><?php echo e($relatedJob->department); ?></span>
                                    <span><?php echo e($relatedJob->location); ?></span>
                                    <span><?php echo e(ucfirst(str_replace('-', ' ', $relatedJob->employment_type))); ?></span>
                                </div>
                            </div>
                            <a href="<?php echo e(route('careers.show', ['locale' => app()->getLocale(), 'job' => $relatedJob])); ?>"
                               class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors">
                                View Details
                            </a>
                        </div>
                    </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>

<?php $__env->startPush('scripts'); ?>
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        alert('Link copied to clipboard!');
    }, function(err) {
        console.error('Could not copy text: ', err);
    });
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\web_devs\chisolution\resources\views/careers/show.blade.php ENDPATH**/ ?>
/**
 * Modern Chat Widget
 * 
 * A floating chat widget that integrates with the ChiSolution Chat API
 * Provides real-time messaging, typing indicators, and modern UI
 */

class ModernChatWidget {
    constructor(options = {}) {
        this.options = {
            apiBaseUrl: options.apiBaseUrl || '/api/v1/chat',
            position: options.position || 'bottom-right',
            theme: options.theme || 'modern',
            autoOpen: options.autoOpen || false,
            enableSound: options.enableSound || true,
            enableNotifications: options.enableNotifications || true,
            ...options
        };

        this.isOpen = false;
        this.isMinimized = false;
        this.currentRoom = null;
        this.messages = [];
        this.unreadCount = 0;
        this.isTyping = false;
        this.typingTimeout = null;
        this.websocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;

        this.init();
    }

    init() {
        this.createWidget();
        this.attachEventListeners();
        this.setupNotifications();
        this.setupKeyboardShortcuts();
        this.setupVisibilityHandling();

        // Try to restore previous chat state
        this.restoreChatState();

        if (this.options.autoOpen) {
            this.open();
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    setupVisibilityHandling() {
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Save state before page unload
        window.addEventListener('beforeunload', () => {
            this.saveChatState();
        });
    }

    createWidget() {
        // Remove existing widget if any
        const existingWidget = document.getElementById('modern-chat-widget');
        if (existingWidget) {
            existingWidget.remove();
        }

        const widgetHTML = `
            <div id="modern-chat-widget" class="modern-chat-widget modern-chat-widget--${this.options.position} modern-chat-widget--${this.options.theme}">
                <!-- Chat Toggle Button -->
                <div class="chat-toggle" id="chat-toggle">
                    <div class="chat-toggle-icon">
                        <svg class="chat-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                        </svg>
                        <svg class="close-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"></line>
                            <line x1="6" y1="6" x2="18" y2="18"></line>
                        </svg>
                    </div>
                    <div class="unread-badge" id="unread-badge" style="display: none;">
                        <span id="unread-count">0</span>
                    </div>
                    <div class="online-indicator" id="online-indicator"></div>
                </div>

                <!-- Chat Window -->
                <div class="chat-window" id="chat-window" style="display: none;">
                    <!-- Header -->
                    <div class="chat-header">
                        <div class="chat-header-info">
                            <div class="chat-title">Live Support</div>
                            <div class="chat-status" id="chat-status">We're here to help!</div>
                        </div>
                        <div class="chat-header-actions">
                            <button class="chat-action-btn" id="minimize-btn" title="Minimize">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                            </button>
                            <button class="chat-action-btn" id="close-btn" title="Close">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="18" y1="6" x2="6" y2="18"></line>
                                    <line x1="6" y1="6" x2="18" y2="18"></line>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div class="chat-messages" id="chat-messages">
                        <!-- Welcome Form (shown initially) -->
                        <div class="welcome-form" id="welcome-form">
                            <div class="welcome-avatar">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div>
                            <div class="welcome-text">
                                <h4>Welcome to Live Support!</h4>
                                <p>Please provide your details to start chatting:</p>
                                <form id="visitor-info-form" class="visitor-form">
                                    <div class="form-group">
                                        <input type="text" id="visitor-name" placeholder="Your Name" required>
                                    </div>
                                    <div class="form-group">
                                        <input type="email" id="visitor-email" placeholder="Your Email" required>
                                    </div>
                                    <button type="submit" class="start-chat-btn">Start Chat</button>
                                </form>
                            </div>
                        </div>

                        <!-- Regular welcome message (shown after form submission) -->
                        <div class="welcome-message" id="welcome-message" style="display: none;">
                            <div class="welcome-avatar">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="12" cy="7" r="4"></circle>
                                </svg>
                            </div>
                            <div class="welcome-text">
                                <h4>Welcome to Live Support!</h4>
                                <p>How can we help you today?</p>
                            </div>
                        </div>
                    </div>

                    <!-- Typing Indicator -->
                    <div class="typing-indicator" id="typing-indicator" style="display: none;">
                        <div class="typing-avatar">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                        </div>
                        <div class="typing-text">
                            <span>Support is typing</span>
                            <div class="typing-dots">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>

                    <!-- Input Area -->
                    <div class="chat-input-area">
                        <div class="chat-input-container">
                            <textarea 
                                id="chat-input" 
                                class="chat-input" 
                                placeholder="Type your message..."
                                rows="1"
                                maxlength="1000"
                            ></textarea>
                            <div class="chat-input-actions">
                                <button class="chat-input-btn" id="emoji-btn" title="Add emoji">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"></path>
                                        <line x1="9" y1="9" x2="9.01" y2="9"></line>
                                        <line x1="15" y1="9" x2="15.01" y2="9"></line>
                                    </svg>
                                </button>
                                <button class="chat-input-btn" id="attach-btn" title="Attach file">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66l-9.2 9.19a2 2 0 0 1-2.83-2.83l8.49-8.48"></path>
                                    </svg>
                                </button>
                                <button class="chat-send-btn" id="send-btn" title="Send message">
                                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <line x1="22" y1="2" x2="11" y2="13"></line>
                                        <polygon points="22,2 15,22 11,13 2,9 22,2"></polygon>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="chat-input-footer">
                            <span class="powered-by">Powered by ChiSolution</span>
                        </div>
                    </div>
                </div>

                <!-- File Input (Hidden) -->
                <input type="file" id="file-input" style="display: none;" accept="image/*,.pdf,.doc,.docx,.txt">
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', widgetHTML);
    }

    attachEventListeners() {
        // Toggle button
        document.getElementById('chat-toggle').addEventListener('click', () => {
            this.toggle();
        });

        // Header actions
        document.getElementById('minimize-btn').addEventListener('click', () => {
            this.minimize();
        });

        document.getElementById('close-btn').addEventListener('click', () => {
            this.close();
        });

        // Input handling
        const chatInput = document.getElementById('chat-input');
        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        chatInput.addEventListener('input', () => {
            this.handleTyping();
            this.autoResizeInput();
        });

        // Send button
        document.getElementById('send-btn').addEventListener('click', () => {
            this.sendMessage();
        });

        // File attachment
        document.getElementById('attach-btn').addEventListener('click', () => {
            document.getElementById('file-input').click();
        });

        document.getElementById('file-input').addEventListener('change', (e) => {
            this.handleFileUpload(e.target.files[0]);
        });

        // Emoji button (placeholder for future emoji picker)
        document.getElementById('emoji-btn').addEventListener('click', () => {
            this.showEmojiPicker();
        });
    }

    setupNotifications() {
        if (this.options.enableNotifications && 'Notification' in window) {
            if (Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }
    }

    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }

    async open() {
        // Check if chat system is available first
        if (!this.currentRoom) {
            const isAvailable = await this.checkChatAvailability();
            if (!isAvailable) {
                this.showError('Chat service is currently unavailable. Please try again later.');
                return;
            }
            await this.createRoom();
        }

        const chatWindow = document.getElementById('chat-window');
        const chatToggle = document.getElementById('chat-toggle');

        chatWindow.style.display = 'flex';
        chatToggle.classList.add('active');
        this.isOpen = true;
        this.isMinimized = false;

        // Clear unread count
        this.unreadCount = 0;
        this.updateUnreadBadge();

        // Focus input
        setTimeout(() => {
            document.getElementById('chat-input').focus();
        }, 300);

        // Load recent messages if room exists
        if (this.currentRoom) {
            await this.loadMessages();
        }
    }

    async checkChatAvailability() {
        try {
            // Simple health check - try to access Laravel's health endpoint
            const response = await fetch('/up', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            return response.ok;
        } catch (error) {
            console.warn('Chat availability check failed:', error);
            // If health check fails, still try to create room (might be a different endpoint structure)
            return true;
        }
    }

    async ensureCsrfToken() {
        // Check if we already have a CSRF token
        const existingToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (existingToken) {
            return;
        }

        try {
            // Try to get CSRF token from Sanctum
            const response = await fetch('/sanctum/csrf-cookie', {
                method: 'GET',
                credentials: 'same-origin'
            });

            if (response.ok) {
                console.log('CSRF token refreshed');
            } else {
                console.warn('Failed to refresh CSRF token:', response.status);
            }
        } catch (error) {
            console.warn('Failed to get CSRF token:', error);
            // Continue without CSRF token - some endpoints might not require it
        }
    }

    buildRoomPayload() {
        // Check if user is authenticated
        const user = window.Laravel?.user || window.authUser || this.getAuthenticatedUser();

        if (user) {
            // Authenticated user
            return {
                type: 'visitor',
                priority: 1,
                language: document.documentElement.lang || 'en',
                visitor_info: {
                    name: user.first_name || user.name || 'User',
                    email: user.email,
                    user_id: user.id,
                    page: window.location.href,
                    user_agent: navigator.userAgent,
                    referrer: document.referrer || '',
                    timestamp: new Date().toISOString(),
                    authenticated: true
                },
                metadata: {
                    source: 'chat_widget',
                    version: '1.0.0',
                    user_type: 'authenticated'
                }
            };
        } else {
            // Anonymous visitor
            return {
                type: 'visitor',
                priority: 1,
                language: document.documentElement.lang || 'en',
                visitor_info: {
                    name: 'Website Visitor',
                    email: '<EMAIL>', // Required field for anonymous users
                    page: window.location.href,
                    user_agent: navigator.userAgent,
                    referrer: document.referrer || '',
                    timestamp: new Date().toISOString(),
                    authenticated: false
                },
                metadata: {
                    source: 'chat_widget',
                    version: '1.0.0',
                    user_type: 'anonymous'
                }
            };
        }
    }

    getAuthenticatedUser() {
        // Try to get user from various sources
        try {
            // Check Laravel global
            if (window.Laravel?.user) {
                return window.Laravel.user;
            }

            // Check meta tag
            const userMeta = document.querySelector('meta[name="user"]');
            if (userMeta) {
                return JSON.parse(userMeta.getAttribute('content'));
            }

            // Check if there's a user API endpoint we can call
            // This would be set up by the backend
            return null;
        } catch (error) {
            console.warn('Failed to get authenticated user:', error);
            return null;
        }
    }

    close() {
        const chatWindow = document.getElementById('chat-window');
        const chatToggle = document.getElementById('chat-toggle');
        
        chatWindow.style.display = 'none';
        chatToggle.classList.remove('active');
        this.isOpen = false;
        this.isMinimized = false;
    }

    minimize() {
        const chatWindow = document.getElementById('chat-window');
        chatWindow.classList.add('minimized');
        this.isMinimized = true;
    }

    async createRoom() {
        try {
            // First, ensure we have a CSRF token
            await this.ensureCsrfToken();

            // Get CSRF token
            const csrfToken = window.Laravel?.csrfToken ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;

            console.log('Creating chat room...', {
                url: `${this.options.apiBaseUrl}/rooms`,
                csrfToken: csrfToken ? 'present' : 'missing'
            });

            // Try without CSRF token first (for anonymous endpoints)
            let response = await fetch(`${this.options.apiBaseUrl}/rooms`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify(this.buildRoomPayload())
            });

            // If CSRF error, retry with CSRF token
            if (response.status === 419 && csrfToken) {
                console.log('Retrying with CSRF token...');
                response = await fetch(`${this.options.apiBaseUrl}/rooms`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify(this.buildRoomPayload())
                });
            }

            console.log('Room creation response:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries())
            });

            if (!response.ok) {
                const errorData = await response.text();
                console.error('Room creation failed:', errorData);
                throw new Error(`HTTP error! status: ${response.status} - ${errorData}`);
            }

            const data = await response.json();
            console.log('Room created successfully:', data);

            if (!data.success || !data.data?.room) {
                throw new Error('Invalid response format from server');
            }

            this.currentRoom = data.data.room;

            // Update status
            document.getElementById('chat-status').textContent = 'Connected';
            document.getElementById('online-indicator').classList.add('online');

            // Save room to localStorage
            this.saveChatState();

            // Connect to real-time updates
            this.connectWebSocket();

        } catch (error) {
            console.error('Failed to create chat room:', error);

            // Show more specific error messages
            let errorMessage = 'Failed to connect to chat. Please try again.';
            if (error.message.includes('403')) {
                errorMessage = 'Access denied. Please refresh the page and try again.';
            } else if (error.message.includes('419')) {
                errorMessage = 'Session expired. Please refresh the page and try again.';
            } else if (error.message.includes('429')) {
                errorMessage = 'Too many requests. Please wait a moment and try again.';
            } else if (error.message.includes('503')) {
                errorMessage = 'Chat service is temporarily unavailable. Please try again later.';
            }

            this.showError(errorMessage);

            // Update status to show error
            document.getElementById('chat-status').textContent = 'Connection failed';
            document.getElementById('online-indicator').classList.remove('online');
        }
    }

    async loadMessages() {
        if (!this.currentRoom) return;

        try {
            const csrfToken = window.Laravel?.csrfToken ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;

            console.log('Loading messages for room:', this.currentRoom.uuid);

            const response = await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/messages`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
                }
            });

            console.log('Load messages response:', {
                status: response.status,
                statusText: response.statusText
            });

            if (response.ok) {
                const data = await response.json();
                console.log('Messages loaded:', data);

                if (data.success && data.data?.messages) {
                    this.messages = data.data.messages;
                    this.renderMessages();
                } else {
                    console.warn('No messages found or invalid response format');
                }
            } else {
                console.error('Failed to load messages:', response.status, response.statusText);
            }
        } catch (error) {
            console.error('Failed to load messages:', error);
        }
    }

    async sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();

        if (!message || !this.currentRoom) return;

        try {
            // Add message to UI immediately (optimistic update)
            const user = this.getAuthenticatedUser();
            const userName = user?.first_name || user?.name || 'You';

            const tempMessage = {
                content: message,
                message_type: 'text',
                user: { name: userName },
                created_at: new Date().toISOString(),
                is_own: true,
                temp_id: Date.now() // Temporary ID for tracking
            };

            this.addMessage(tempMessage);

            // Clear input
            input.value = '';
            this.autoResizeInput();

            // Get CSRF token
            const csrfToken = window.Laravel?.csrfToken ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;

            console.log('Sending message...', {
                roomUuid: this.currentRoom.uuid,
                message: message.substring(0, 50) + '...',
                csrfToken: csrfToken ? 'present' : 'missing'
            });

            // Send to API (try without CSRF token first)
            let response = await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/messages`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    content: message,
                    message_type: 'text',
                    metadata: {
                        source: 'chat_widget',
                        timestamp: new Date().toISOString()
                    }
                })
            });

            // If CSRF error, retry with CSRF token
            if (response.status === 419 && csrfToken) {
                console.log('Retrying message send with CSRF token...');
                response = await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/messages`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': csrfToken
                    },
                    body: JSON.stringify({
                        content: message,
                        message_type: 'text',
                        metadata: {
                            source: 'chat_widget',
                            timestamp: new Date().toISOString()
                        }
                    })
                });
            }

            console.log('Message send response:', {
                status: response.status,
                statusText: response.statusText
            });

            if (!response.ok) {
                const errorData = await response.text();
                console.error('Message send failed:', errorData);
                throw new Error(`HTTP error! status: ${response.status} - ${errorData}`);
            }

            const data = await response.json();
            console.log('Message sent successfully:', data);

            // Update the temporary message with the real message data
            if (data.success && data.data?.message) {
                // Find and update the temporary message
                const messageElements = document.querySelectorAll('.chat-message--own');
                const lastMessage = messageElements[messageElements.length - 1];
                if (lastMessage) {
                    const statusElement = lastMessage.querySelector('.message-status');
                    if (statusElement) {
                        statusElement.textContent = '✓✓'; // Mark as delivered
                    }
                }
            }

        } catch (error) {
            console.error('Failed to send message:', error);

            // Remove the optimistic message and show error
            const messageElements = document.querySelectorAll('.chat-message--own');
            const lastMessage = messageElements[messageElements.length - 1];
            if (lastMessage) {
                lastMessage.style.opacity = '0.5';
                lastMessage.style.border = '1px solid #ef4444';

                // Add retry button
                const retryBtn = document.createElement('button');
                retryBtn.textContent = 'Retry';
                retryBtn.className = 'text-xs bg-red-500 text-white px-2 py-1 rounded mt-1';
                retryBtn.onclick = () => {
                    input.value = message;
                    lastMessage.remove();
                    this.sendMessage();
                };
                lastMessage.querySelector('.message-content').appendChild(retryBtn);
            }

            let errorMessage = 'Failed to send message. Please try again.';
            if (error.message.includes('403')) {
                errorMessage = 'Access denied. Please refresh the page.';
            } else if (error.message.includes('419')) {
                errorMessage = 'Session expired. Please refresh the page.';
            }

            this.showError(errorMessage);
        }
    }

    addMessage(message) {
        this.messages.push(message);
        this.renderMessage(message);
        this.scrollToBottom();

        // Play sound for incoming messages
        if (!message.is_own && this.options.enableSound) {
            this.playNotificationSound();
        }

        // Show notification for incoming messages when widget is closed
        if (!message.is_own && !this.isOpen && this.options.enableNotifications) {
            this.showNotification(message);
            this.unreadCount++;
            this.updateUnreadBadge();
        }
    }

    renderMessages() {
        const messagesContainer = document.getElementById('chat-messages');
        // Clear existing messages except welcome message
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        messagesContainer.innerHTML = '';
        if (welcomeMessage) {
            messagesContainer.appendChild(welcomeMessage);
        }

        this.messages.forEach(message => {
            this.renderMessage(message);
        });

        this.scrollToBottom();
    }

    renderMessage(message) {
        const messagesContainer = document.getElementById('chat-messages');
        const messageElement = document.createElement('div');

        const isOwn = message.is_own || (message.user && message.user.name === 'You');
        const isAI = message.is_ai_generated;

        messageElement.className = `chat-message ${isOwn ? 'chat-message--own' : 'chat-message--other'} ${isAI ? 'chat-message--ai' : ''}`;

        const timestamp = new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageElement.innerHTML = `
            ${!isOwn ? `
                <div class="message-avatar">
                    ${isAI ? `
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-6.5l-4.24 4.24M7.76 7.76L3.52 3.52m12.96 12.96l-4.24-4.24M7.76 16.24l-4.24 4.24"></path>
                        </svg>
                    ` : `
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                    `}
                </div>
            ` : ''}
            <div class="message-content">
                <div class="message-text">${this.formatMessageContent(message.content)}</div>
                <div class="message-meta">
                    <span class="message-time">${timestamp}</span>
                    ${isOwn ? '<span class="message-status">✓</span>' : ''}
                </div>
            </div>
        `;

        messagesContainer.appendChild(messageElement);

        // Animate in
        setTimeout(() => {
            messageElement.classList.add('message-visible');
        }, 10);
    }

    formatMessageContent(content) {
        // Basic HTML escaping and link detection
        return content
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;')
            .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener">$1</a>')
            .replace(/\n/g, '<br>');
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    autoResizeInput() {
        const input = document.getElementById('chat-input');
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 120) + 'px';
    }

    handleTyping() {
        if (!this.currentRoom) return;

        // Clear existing timeout
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        // Send typing indicator
        if (!this.isTyping) {
            this.isTyping = true;
            this.sendTypingIndicator(true);
        }

        // Set timeout to stop typing
        this.typingTimeout = setTimeout(() => {
            this.isTyping = false;
            this.sendTypingIndicator(false);
        }, 1000);
    }

    async sendTypingIndicator(isTyping) {
        if (!this.currentRoom) return;

        try {
            const csrfToken = window.Laravel?.csrfToken ||
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             document.querySelector('input[name="_token"]')?.value;

            await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/typing`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...(csrfToken && { 'X-CSRF-TOKEN': csrfToken })
                },
                body: JSON.stringify({
                    is_typing: isTyping
                })
            });
        } catch (error) {
            // Silently fail for typing indicators as they're not critical
            console.debug('Failed to send typing indicator:', error);
        }
    }

    updateUnreadBadge() {
        const badge = document.getElementById('unread-badge');
        const count = document.getElementById('unread-count');

        if (this.unreadCount > 0) {
            count.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    showError(message) {
        // Show error in chat
        this.addMessage({
            content: message,
            message_type: 'system',
            user: { name: 'System' },
            created_at: new Date().toISOString(),
            is_own: false,
            is_error: true
        });
    }

    showNotification(message) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification('New message from Support', {
                body: message.content.substring(0, 100),
                icon: '/favicon.ico',
                tag: 'chat-message'
            });
        }
    }

    playNotificationSound() {
        // Simple notification sound using Web Audio API
        try {
            const AudioContextClass = window.AudioContext || (window.webkitAudioContext || null);
            if (!AudioContextClass) return;

            const audioContext = new AudioContextClass();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.value = 800;
            oscillator.type = 'sine';

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.1);
        } catch (error) {
            console.log('Could not play notification sound:', error);
        }
    }

    async handleFileUpload(file) {
        if (!file || !this.currentRoom) return;

        // Basic file validation
        const maxSize = 10 * 1024 * 1024; // 10MB
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'text/plain'];

        if (file.size > maxSize) {
            this.showError('File size must be less than 10MB');
            return;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showError('File type not supported. Please upload images, PDFs, or text files.');
            return;
        }

        try {
            // Show upload progress
            this.showUploadProgress(file.name);

            const formData = new FormData();
            formData.append('file', file);
            formData.append('message_type', 'file');

            const response = await fetch(`${this.options.apiBaseUrl}/rooms/${this.currentRoom.uuid}/messages`, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': window.Laravel?.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                },
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            // Add file message to UI
            this.addMessage({
                content: `📎 ${file.name}`,
                message_type: 'file',
                user: { name: 'You' },
                created_at: new Date().toISOString(),
                is_own: true,
                file_url: data.data.message.file_url
            });

            this.hideUploadProgress();

        } catch (error) {
            console.error('Failed to upload file:', error);
            this.showError('Failed to upload file. Please try again.');
            this.hideUploadProgress();
        }
    }

    showUploadProgress(fileName) {
        const messagesContainer = document.getElementById('chat-messages');
        const progressElement = document.createElement('div');
        progressElement.id = 'upload-progress';
        progressElement.className = 'chat-message chat-message--own';
        progressElement.innerHTML = `
            <div class="message-content">
                <div class="message-text">
                    <div class="flex items-center gap-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Uploading ${fileName}...</span>
                    </div>
                </div>
            </div>
        `;
        messagesContainer.appendChild(progressElement);
        this.scrollToBottom();
    }

    hideUploadProgress() {
        const progressElement = document.getElementById('upload-progress');
        if (progressElement) {
            progressElement.remove();
        }
    }

    showEmojiPicker() {
        const input = document.getElementById('chat-input');
        const commonEmojis = ['😊', '😂', '👍', '👎', '❤️', '😢', '😮', '😡', '🤔', '👋', '🙏', '✅', '❌', '⭐', '🎉'];

        // Create emoji picker if it doesn't exist
        let emojiPicker = document.getElementById('emoji-picker');
        if (!emojiPicker) {
            emojiPicker = document.createElement('div');
            emojiPicker.id = 'emoji-picker';
            emojiPicker.className = 'absolute bottom-12 right-0 bg-white border border-gray-200 rounded-lg shadow-lg p-3 grid grid-cols-5 gap-2 z-10';
            emojiPicker.style.display = 'none';

            commonEmojis.forEach(emoji => {
                const emojiBtn = document.createElement('button');
                emojiBtn.textContent = emoji;
                emojiBtn.className = 'text-xl hover:bg-gray-100 rounded p-1 transition-colors';
                emojiBtn.onclick = () => {
                    input.value += emoji;
                    input.focus();
                    emojiPicker.style.display = 'none';
                };
                emojiPicker.appendChild(emojiBtn);
            });

            document.querySelector('.chat-input-container').appendChild(emojiPicker);
        }

        // Toggle emoji picker
        if (emojiPicker.style.display === 'none') {
            emojiPicker.style.display = 'grid';
            // Hide when clicking outside
            setTimeout(() => {
                document.addEventListener('click', function hideEmojiPicker(e) {
                    if (!emojiPicker.contains(e.target) && !document.getElementById('emoji-btn').contains(e.target)) {
                        emojiPicker.style.display = 'none';
                        document.removeEventListener('click', hideEmojiPicker);
                    }
                });
            }, 100);
        } else {
            emojiPicker.style.display = 'none';
        }
    }

    // Add method to handle keyboard shortcuts
    handleKeyboardShortcuts(event) {
        // Escape key to close chat
        if (event.key === 'Escape' && this.isOpen) {
            this.close();
        }

        // Ctrl/Cmd + K to open chat
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            this.toggle();
        }
    }

    // Add method to save chat state to localStorage
    saveChatState() {
        if (this.currentRoom) {
            localStorage.setItem('chat_room_uuid', this.currentRoom.uuid);
            localStorage.setItem('chat_messages', JSON.stringify(this.messages.slice(-50))); // Save last 50 messages
        }
    }

    // Add method to restore chat state from localStorage
    restoreChatState() {
        const roomUuid = localStorage.getItem('chat_room_uuid');
        const savedMessages = localStorage.getItem('chat_messages');

        if (roomUuid && savedMessages) {
            try {
                this.currentRoom = { uuid: roomUuid };
                this.messages = JSON.parse(savedMessages);
                this.renderMessages();

                // Update UI state
                document.getElementById('chat-status').textContent = 'Reconnecting...';

                // Verify room still exists
                this.verifyRoom(roomUuid);
            } catch (error) {
                console.error('Failed to restore chat state:', error);
                localStorage.removeItem('chat_room_uuid');
                localStorage.removeItem('chat_messages');
            }
        }
    }

    async verifyRoom(roomUuid) {
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/rooms/${roomUuid}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': window.Laravel?.csrfToken || document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.currentRoom = data.data.room;
                document.getElementById('chat-status').textContent = 'Connected';
                document.getElementById('online-indicator').classList.add('online');
                this.connectWebSocket();
                await this.loadMessages(); // Refresh messages
            } else {
                // Room no longer exists, create new one
                localStorage.removeItem('chat_room_uuid');
                localStorage.removeItem('chat_messages');
                this.currentRoom = null;
                this.messages = [];
                await this.createRoom();
            }
        } catch (error) {
            console.error('Failed to verify room:', error);
            // Fallback to creating new room
            this.currentRoom = null;
            await this.createRoom();
        }
    }

    // Add method to handle window visibility changes
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden, save state
            this.saveChatState();
        } else {
            // Page is visible, clear unread count if chat is open
            if (this.isOpen) {
                this.unreadCount = 0;
                this.updateUnreadBadge();
            }
        }
    }

    connectWebSocket() {
        if (!this.currentRoom || !window.Echo) {
            console.log('WebSocket not available or no room');
            return;
        }

        try {
            // For visitor rooms, use public channel (no authentication required)
            // For other rooms, use private channel
            const isVisitorRoom = this.currentRoom.type === 'visitor';
            const channelName = isVisitorRoom
                ? `public.chat.room.${this.currentRoom.uuid}`
                : `private-chat.room.${this.currentRoom.uuid}`;

            const channel = isVisitorRoom
                ? window.Echo.channel(channelName)
                : window.Echo.private(channelName);

            // Listen for new messages
            channel.listen('.message.sent', (data) => {
                if (data.message && !data.message.is_own) {
                    this.addMessage({
                        ...data.message,
                        is_own: false
                    });
                }
            });

            // Listen for typing indicators
            channel.listen('.user.typing', (data) => {
                this.handleTypingIndicator(data);
            });

            // Listen for user online status
            channel.listen('.user.online.status', (data) => {
                this.handleOnlineStatus(data);
            });

            // Listen for message read receipts
            channel.listen('.message.read', (data) => {
                this.handleMessageRead(data);
            });

            console.log('WebSocket connected to room:', this.currentRoom.uuid);

        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
        }
    }

    handleTypingIndicator(data) {
        const typingIndicator = document.getElementById('typing-indicator');

        if (data.is_typing && data.user_type !== 'visitor') {
            typingIndicator.style.display = 'flex';
            typingIndicator.classList.add('show');

            // Auto-hide after 3 seconds
            setTimeout(() => {
                typingIndicator.style.display = 'none';
                typingIndicator.classList.remove('show');
            }, 3000);
        } else {
            typingIndicator.style.display = 'none';
            typingIndicator.classList.remove('show');
        }
    }

    handleOnlineStatus(data) {
        const onlineIndicator = document.getElementById('online-indicator');
        const chatStatus = document.getElementById('chat-status');

        if (data.is_online && data.user_type === 'staff') {
            onlineIndicator.classList.add('online');
            chatStatus.textContent = 'Support is online';
        } else {
            onlineIndicator.classList.remove('online');
            chatStatus.textContent = 'We\'ll respond soon';
        }
    }

    handleMessageRead(data) {
        // Update message status to show it was read
        console.log('Message read event:', data);
        const messageElements = document.querySelectorAll('.chat-message--own .message-status');
        messageElements.forEach(element => {
            if (element.textContent === '✓') {
                element.textContent = '✓✓';
                element.style.color = '#3b82f6';
            }
        });
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if not in admin area and chat is enabled
    const isAdminArea = window.location.pathname.includes('/admin');
    const isChatDisabled = document.querySelector('meta[name="chat-disabled"]');

    // Check if we're in a test environment
    const isTestEnvironment = window.location.hostname === 'localhost' ||
                             window.location.hostname === '127.0.0.1' ||
                             window.location.hostname.includes('test');

    if (!isAdminArea && !isChatDisabled) {
        try {
            // Determine the correct API base URL
            let apiBaseUrl = '/api/v1/chat';

            // Use configured routes if available
            if (window.appConfig?.routes?.chatApi) {
                apiBaseUrl = window.appConfig.routes.chatApi;
            }

            console.log('Initializing chat widget with config:', {
                apiBaseUrl,
                isTestEnvironment,
                currentPath: window.location.pathname,
                csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ? 'present' : 'missing'
            });

            window.chatWidget = new ModernChatWidget({
                apiBaseUrl: apiBaseUrl,
                enableNotifications: !isTestEnvironment, // Disable notifications in test
                enableSound: !isTestEnvironment, // Disable sound in test
                position: 'bottom-right',
                theme: 'modern'
            });

            console.log('Chat widget initialized successfully');
        } catch (error) {
            console.error('Failed to initialize chat widget:', error);
        }
    } else {
        console.log('Chat widget not initialized:', {
            isAdminArea,
            isChatDisabled: !!isChatDisabled,
            currentPath: window.location.pathname
        });
    }
});

// Export for global access
window.ModernChatWidget = ModernChatWidget;

// Add global helper functions
window.openChat = function() {
    if (window.chatWidget) {
        window.chatWidget.open();
    }
};

window.closeChat = function() {
    if (window.chatWidget) {
        window.chatWidget.close();
    }
};

window.toggleChat = function() {
    if (window.chatWidget) {
        window.chatWidget.toggle();
    }
};

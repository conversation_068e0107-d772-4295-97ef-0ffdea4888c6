<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use App\Services\VisitorAnalytics;

class ProfileController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    /**
     * Create a new controller instance.
     */
    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->middleware('auth');
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Show the profile edit form.
     */
    public function edit(): View
    {
        $user = auth()->user();

        // Track profile edit page visit
        $this->visitorAnalytics->trackPageVisit(
            'Profile Edit',
            [
                'user_id' => $user->id,
                'action' => 'view_profile_edit',
            ]
        );

        return view('profile.edit', compact('user'));
    }

    /**
     * Update the user's profile information.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'phone' => ['nullable', 'string', 'max:20'],
            'avatar' => ['nullable', 'image', 'mimes:jpeg,png,jpg,gif', 'max:2048'],
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            // Store new avatar
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        // Check if email changed (requires re-verification)
        $emailChanged = $user->email !== $validated['email'];

        // Update user
        $user->update($validated);

        // Track profile update in visitor analytics
        $this->visitorAnalytics->trackFormInteraction(
            'profile_update_form',
            'submit',
            true,
            [
                'user_id' => $user->id,
                'fields_updated' => array_keys($validated),
                'email_changed' => $emailChanged,
                'avatar_updated' => $request->hasFile('avatar'),
            ]
        );

        // If email changed, mark as unverified
        if ($emailChanged) {
            $user->email_verified_at = null;
            $user->save();

            // Send verification email
            $user->sendEmailVerificationNotification();

            return redirect()->route('profile.edit')
                ->with('success', 'Profile updated successfully! Please check your email to verify your new email address.');
        }

        return redirect()->route('profile.edit')
            ->with('success', 'Profile updated successfully!');
    }

    /**
     * Update the user's password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'current_password' => ['required', 'string'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
        ]);

        // Verify current password
        if (!Hash::check($validated['current_password'], $user->password)) {
            return back()->withErrors(['current_password' => 'The current password is incorrect.']);
        }

        // Update password
        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()->route('profile.edit')
            ->with('success', 'Password updated successfully!');
    }

    /**
     * Delete the user's avatar.
     */
    public function deleteAvatar(): RedirectResponse
    {
        $user = auth()->user();

        if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
            Storage::disk('public')->delete($user->avatar);
        }

        $user->update(['avatar' => null]);

        return redirect()->route('profile.edit')
            ->with('success', 'Avatar deleted successfully!');
    }

    /**
     * Show the account deletion confirmation page.
     */
    public function deleteAccount(): View
    {
        return view('profile.delete-account');
    }

    /**
     * Delete the user's account.
     */
    public function destroyAccount(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $request->validate([
            'password' => ['required', 'string'],
            'confirmation' => ['required', 'string', 'in:DELETE'],
        ]);

        // Verify password
        if (!Hash::check($request->password, $user->password)) {
            return back()->withErrors(['password' => 'The password is incorrect.']);
        }

        // Soft delete the user
        $user->update([
            'is_deleted' => true,
            'is_active' => false,
        ]);

        // Logout the user
        auth()->logout();

        return redirect()->route('home')
            ->with('success', 'Your account has been deleted successfully.');
    }
}

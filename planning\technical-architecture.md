# Technical Architecture Document
## ChiSolution Digital Agency Platform

### 🏗️ System Architecture Overview

#### **Architecture Pattern: Service-Oriented MVC + Event-Driven Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Web UI    │  │  Admin UI   │  │   API       │         │
│  │  (Blade)    │  │  (Blade)    │  │ (JSON)      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ Controllers │  │ Middleware  │  │ Form        │         │
│  │             │  │             │  │ Requests    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LAYER                           │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Services   │  │   Actions   │  │   Events    │         │
│  │             │  │             │  │             │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     DATA LAYER                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   Models    │  │ Repositories│  │   Cache     │         │
│  │ (Eloquent)  │  │             │  │   (Redis)   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   MySQL     │  │   Storage   │  │   Queue     │         │
│  │  Database   │  │   (Files)   │  │  (Redis)    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 🛠️ Technology Stack

#### **Backend Technologies**
- **Framework**: Laravel 12.19.3 (PHP 8.2+)
- **Database**: MySQL with comprehensive migrations and indexing
- **Authentication**: Laravel Sanctum for API authentication
- **Queue System**: Redis-based queue processing
- **Cache**: Redis for session and application caching
- **File Storage**: Local storage with S3 compatibility
- **Email**: SMTP with queue-based sending

#### **Frontend Technologies**
- **CSS Framework**: Tailwind CSS 4.x (exclusively)
- **Build Tool**: Vite for asset compilation and hot reloading
- **JavaScript**: Vanilla JS with Alpine.js components
- **Icons**: Heroicons and custom SVG icons
- **Fonts**: Inter font family for modern typography

#### **Third-Party Integrations**
- **Payment Processing**: Stripe integration with webhook support
- **Image Processing**: Intervention Image with Spatie optimization
- **Email Marketing**: Custom email campaign system
- **Analytics**: Custom visitor analytics and journey tracking
- **Security**: ClamAV virus scanning (optional)
- **Device Detection**: Jenssegers Agent for device/browser detection

#### **Development Tools**
- **Package Manager**: Composer (PHP), NPM (JavaScript)
- **Code Quality**: Laravel Pint for code formatting
- **Testing**: PHPUnit for backend testing
- **Development Server**: Laravel Artisan serve with Vite dev server
- **Concurrency**: Concurrently for running multiple dev processes

#### **Production Infrastructure**
- **Web Server**: Nginx or Apache with PHP-FPM
- **Database**: MySQL 8.0+ with optimized configuration
- **Cache**: Redis for session storage and application cache
- **Queue Worker**: Supervisor for queue processing
- **File Storage**: Local storage with backup to cloud storage
- **SSL**: Let's Encrypt or commercial SSL certificates

### 📁 Folder Structure

```
app/
├── Actions/                    # Single-purpose action classes
│   ├── Auth/
│   ├── Orders/
│   ├── Products/
│   └── Projects/
├── Console/
│   └── Commands/
├── Events/                     # Domain events
├── Exceptions/
├── Http/
│   ├── Controllers/
│   │   ├── Admin/             # Admin dashboard controllers
│   │   ├── Api/               # API controllers
│   │   ├── Auth/              # Authentication controllers
│   │   └── Web/               # Public web controllers
│   ├── Middleware/
│   ├── Requests/              # Form request validation
│   └── Resources/             # API resources
├── Jobs/                      # Queue jobs
├── Listeners/                 # Event listeners
├── Mail/                      # Mail classes
├── Models/
│   ├── Concerns/              # Model traits
│   └── Scopes/                # Query scopes
├── Notifications/
├── Policies/                  # Authorization policies
├── Providers/
├── Repositories/              # Data access layer
│   ├── Contracts/             # Repository interfaces
│   └── Eloquent/              # Eloquent implementations
├── Services/                  # Business logic services
│   ├── Accounting/            # Accounting services
│   │   ├── AccountingClientService.php
│   │   ├── TransactionService.php
│   │   ├── PayrollService.php
│   │   ├── ReportingService.php
│   │   └── TaxService.php
│   ├── Auth/
│   ├── Cart/
│   ├── Order/
│   ├── Payment/
│   ├── Product/
│   ├── Project/
│   └── Seo/
└── Traits/                    # Reusable traits

config/
├── app.php
├── database.php
├── filesystems.php
├── localization.php           # Custom localization config
├── payment.php                # Payment gateway config
├── seo.php                    # SEO configuration
└── services.php

database/
├── factories/
├── migrations/
├── seeders/
└── schema/                    # Database documentation

resources/
├── css/
├── js/
├── lang/                      # Translation files
│   ├── en/
│   ├── fr/
│   └── es/
└── views/
    ├── admin/                 # Admin dashboard views
    ├── auth/                  # Authentication views
    ├── components/            # Blade components
    ├── emails/                # Email templates
    ├── layouts/               # Layout templates
    └── web/                   # Public website views
```

### 🎨 Frontend Architecture

#### **CSS Architecture**
```css
resources/css/app.css Structure:
├── Base Styles
│   ├── CSS Reset & Normalize
│   ├── Typography System
│   └── Color Variables
├── Component Styles
│   ├── Buttons (.btn-primary, .btn-secondary)
│   ├── Cards (.card, .card-hover)
│   ├── Navigation (.nav-link, .nav-link-active)
│   └── Forms (Floating Label System)
├── Layout Styles
│   ├── Grid System
│   ├── Containers
│   └── Spacing Utilities
└── Utility Classes
    ├── Text Utilities
    ├── Spacing Utilities
    └── Display Utilities

Floating Label Form System:
- .floating-input-group: Container wrapper
- .floating-input: Input/textarea/select element
- .floating-label: Animated label element
- States: default, focus, filled, error, success, disabled
```

#### **JavaScript Architecture**
```javascript
resources/js/ Structure:
├── app.js                     # Main application entry
├── components/
│   ├── floating-forms.js      # Floating label functionality
│   ├── navigation.js          # Navigation interactions
│   ├── cart.js               # Shopping cart functionality
│   └── accordion.js          # FAQ accordion component
├── pages/
│   ├── shop.js               # E-commerce specific JS
│   ├── contact.js            # Contact form handling
│   └── checkout.js           # Checkout process
└── utils/
    ├── api.js                # API communication helpers
    ├── validation.js         # Client-side validation
    └── helpers.js            # Utility functions

Form Enhancement Pattern:
1. Progressive Enhancement (works without JS)
2. Event delegation for dynamic content
3. State management for form interactions
4. Validation feedback integration
```

#### **Blade Component System**
```php
resources/views/components/ Structure:
├── forms/
│   ├── floating-input.blade.php    # Floating label input
│   ├── floating-textarea.blade.php # Floating label textarea
│   ├── floating-select.blade.php   # Floating label select
│   └── form-group.blade.php        # Form group wrapper
├── ui/
│   ├── button.blade.php            # Reusable button component
│   ├── card.blade.php              # Card component
│   ├── modal.blade.php             # Modal component
│   └── accordion.blade.php         # Accordion component
└── layout/
    ├── navigation.blade.php        # Main navigation
    ├── footer.blade.php            # Site footer
    └── breadcrumbs.blade.php       # Breadcrumb navigation

Usage Example:
<x-forms.floating-input
    name="email"
    type="email"
    label="Email Address"
    required />
```

storage/
├── app/
│   ├── public/
│   │   ├── products/          # Product images
│   │   ├── projects/          # Project images
│   │   ├── team/              # Team member photos
│   │   └── uploads/           # General uploads
├── framework/
└── logs/

tests/
├── Feature/                   # Feature tests
│   ├── Admin/
│   ├── Api/
│   ├── Auth/
│   └── Web/
├── Unit/                      # Unit tests
│   ├── Actions/
│   ├── Models/
│   ├── Repositories/
│   └── Services/
└── TestCase.php
```

### 🔧 Design Patterns Implementation

#### **Repository Pattern**
```php
// Repository Interface
interface ProductRepositoryInterface
{
    public function findBySlug(string $slug): ?Product;
    public function getActiveProducts(array $filters = []): Collection;
    public function getFeaturedProducts(int $limit = 10): Collection;
}

// Eloquent Implementation
class EloquentProductRepository implements ProductRepositoryInterface
{
    public function findBySlug(string $slug): ?Product
    {
        return Product::where('slug', $slug)
            ->where('is_active', true)
            ->where('is_deleted', false)
            ->first();
    }
}
```

#### **Service Layer Pattern**
```php
class ProductService
{
    public function __construct(
        private ProductRepositoryInterface $productRepository,
        private SeoService $seoService,
        private ImageService $imageService
    ) {}

    public function createProduct(array $data): Product
    {
        DB::beginTransaction();
        try {
            $product = $this->productRepository->create($data);
            $this->seoService->generateSeoMeta($product);
            $this->imageService->processProductImages($product, $data['images']);
            
            DB::commit();
            return $product;
        } catch (Exception $e) {
            DB::rollback();
            throw $e;
        }
    }
}
```

#### **Action Pattern**
```php
class CreateOrderAction
{
    public function execute(array $cartItems, array $customerData): Order
    {
        return DB::transaction(function () use ($cartItems, $customerData) {
            $order = Order::create($this->prepareOrderData($customerData));
            $this->createOrderItems($order, $cartItems);
            $this->updateInventory($cartItems);
            $this->sendOrderConfirmation($order);
            
            return $order;
        });
    }
}
```

### 🔐 Security Architecture

#### **Authentication Flow**
```
User Request → Middleware → Controller → Service → Repository → Database
     ↓              ↓           ↓          ↓          ↓
  Sanitize → Authenticate → Authorize → Validate → Execute
```

#### **Security Layers**
1. **Input Validation**: Form Requests with custom rules
2. **Authentication**: Laravel Sanctum for API, Session for web
3. **Authorization**: Policies and Gates for fine-grained control
4. **CSRF Protection**: Built-in Laravel CSRF middleware
5. **SQL Injection**: Eloquent ORM with parameter binding
6. **XSS Protection**: Blade template escaping
7. **Rate Limiting**: Custom rate limiters per endpoint

### 📊 Data Flow Architecture

#### **Request Lifecycle**
```
1. Route Resolution
   ↓
2. Middleware Stack
   ├── CORS
   ├── Authentication
   ├── Authorization
   ├── Localization
   └── Rate Limiting
   ↓
3. Controller
   ├── Form Request Validation
   ├── Service Layer Call
   └── Response Formatting
   ↓
4. Service Layer
   ├── Business Logic
   ├── Repository Calls
   ├── Event Dispatching
   └── Cache Management
   ↓
5. Repository Layer
   ├── Database Queries
   ├── Model Relationships
   └── Data Transformation
   ↓
6. Response
   ├── View Rendering (Web)
   ├── JSON Response (API)
   └── Redirect (Forms)
```

### 🚀 Performance Architecture

#### **Caching Strategy**
```php
// Multi-level caching
1. Application Cache (Redis)
   - User sessions
   - Query results
   - Computed data

2. Database Query Cache
   - Eloquent model caching
   - Query result caching

3. View Cache
   - Blade template compilation
   - Partial view caching

4. Asset Cache
   - CSS/JS compilation
   - Image optimization
   - CDN integration
```

#### **Queue Architecture**
```php
// Queue Jobs
├── Email Notifications (high priority)
├── Image Processing (medium priority)
├── SEO Sitemap Generation (low priority)
├── Analytics Data Processing (low priority)
└── Backup Operations (low priority)
```

### 🔄 Event-Driven Architecture

#### **Domain Events**
```php
// Order Events
OrderCreated::class → [
    SendOrderConfirmationEmail::class,
    UpdateInventoryLevels::class,
    CreateInvoice::class,
    NotifyAdmins::class
]

// User Events
UserRegistered::class → [
    SendWelcomeEmail::class,
    CreateUserProfile::class,
    AssignDefaultRole::class
]

// Product Events
ProductCreated::class → [
    GenerateSeoMeta::class,
    ProcessProductImages::class,
    UpdateSitemap::class
]
```

### 📱 API Architecture

#### **RESTful API Design**
```
GET    /api/v1/products              # List products
GET    /api/v1/products/{id}         # Get product
POST   /api/v1/products              # Create product
PUT    /api/v1/products/{id}         # Update product
DELETE /api/v1/products/{id}         # Delete product

GET    /api/v1/orders                # List orders
GET    /api/v1/orders/{id}           # Get order
POST   /api/v1/orders                # Create order
PUT    /api/v1/orders/{id}/status    # Update order status
```

#### **API Response Format**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "Product Name",
        "price": 999.99
    },
    "meta": {
        "pagination": {
            "current_page": 1,
            "total_pages": 10,
            "per_page": 20,
            "total": 200
        }
    },
    "links": {
        "self": "/api/v1/products/1",
        "related": "/api/v1/products/1/variants"
    }
}
```

### 🔧 Development Standards

#### **Code Standards**
- PSR-12 coding standards
- Laravel best practices
- SOLID principles
- DRY (Don't Repeat Yourself)
- KISS (Keep It Simple, Stupid)

---

## 🏗️ Service Architecture

### 📊 Core Business Services

#### **Implemented Services**
```
app/Services/
├── ActivityLogger.php              # Comprehensive activity logging and audit trails
├── CartService.php                 # Shopping cart management and calculations
├── CircuitBreakerService.php       # Circuit breaker pattern for external services
├── DashboardCacheService.php       # Dashboard data caching and optimization
├── EmailNotificationService.php    # Email notification management
├── FileService.php                 # Secure file upload and processing
├── GlobalSearchService.php         # Global search across all entities
├── ImageService.php                # Image processing and optimization
├── OrderService.php                # Order processing and management
├── PaginationService.php           # Advanced pagination with caching
├── PaymentService.php              # Payment processing and webhook handling
├── RecentActivityService.php       # Recent activity tracking and display
├── ShopCacheService.php            # E-commerce caching and performance
├── VisitorAnalytics.php            # Visitor tracking and journey mapping
└── VisitorAnalyticsDashboard.php   # Analytics dashboard data aggregation
```

#### **Service Layer Architecture Pattern**
```php
// Example Service Implementation
class OrderService
{
    public function __construct(
        private OrderRepository $orderRepository,
        private PaymentService $paymentService,
        private EmailNotificationService $emailService,
        private ActivityLogger $activityLogger,
        private VisitorAnalytics $visitorAnalytics
    ) {}

    public function createOrder(CreateOrderDTO $dto): Order
    {
        DB::beginTransaction();
        try {
            // Create order
            $order = $this->orderRepository->create($dto->toArray());

            // Process payment if required
            if ($dto->paymentMethod !== 'pay_offline') {
                $payment = $this->paymentService->processPayment($order, $dto->paymentData);
                $order->update(['payment_status' => $payment->status]);
            }

            // Send notifications
            $this->emailService->sendOrderConfirmation($order);

            // Log activity
            $this->activityLogger->logOrderCreated($order);

            // Track conversion
            $this->visitorAnalytics->trackConversion('order_created', [
                'order_id' => $order->id,
                'total_amount' => $order->total_amount
            ]);

            DB::commit();
            return $order;
        } catch (Exception $e) {
            DB::rollback();
            throw new OrderProcessingException('Failed to create order: ' . $e->getMessage());
        }
    }
}
```

#### **Advanced Service Features**

**1. Visitor Analytics Service**
- Real-time visitor tracking with device fingerprinting
- Journey mapping and conversion funnel analysis
- Lead scoring and qualification
- Checkout abandonment tracking
- Performance metrics and user behavior analysis

**2. Image Service**
- WebP conversion and optimization
- Multiple size generation (thumbnail, small, medium, large, hero)
- Security scanning and content validation
- Metadata removal and EXIF stripping
- Watermark application and batch processing

**3. File Service**
- Virus scanning with ClamAV integration
- Content type validation and security checks
- File quarantine system for infected files
- Metadata extraction and analysis
- Secure file storage with access controls

**4. Activity Logger**
- Comprehensive audit trails for all user actions
- Risk scoring and suspicious activity detection
- Performance monitoring and error tracking
- Compliance logging for regulatory requirements
- Real-time security monitoring and alerting

**5. Email Marketing Service**
- Template-based email campaigns
- Subscriber segmentation and tagging
- A/B testing and campaign optimization
- Open/click tracking with pixel tracking
- Automated drip campaigns and sequences

### 📊 Accounting Services Architecture (Planned)

#### **Accounting Module Structure**
```
app/Services/Accounting/
├── AccountingClientService.php    # Client management and onboarding
├── TransactionService.php         # Financial transaction processing
├── PayrollService.php            # Payroll processing and compliance
├── ReportingService.php          # Financial report generation
├── TaxService.php                # Tax calculations and filing
├── Contracts/                    # Service interfaces
│   ├── AccountingClientServiceInterface.php
│   ├── TransactionServiceInterface.php
│   ├── PayrollServiceInterface.php
│   ├── ReportingServiceInterface.php
│   └── TaxServiceInterface.php
└── DTOs/                         # Data Transfer Objects
    ├── ClientOnboardingDTO.php
    ├── TransactionDTO.php
    ├── PayrollRunDTO.php
    └── ReportGenerationDTO.php
```

#### **Accounting Service Implementation**
```php
class AccountingClientService implements AccountingClientServiceInterface
{
    public function __construct(
        private AccountingClientRepository $clientRepository,
        private UserRepository $userRepository,
        private NotificationService $notificationService,
        private ActivityLogger $activityLogger
    ) {}

    public function onboardClient(ClientOnboardingDTO $dto): AccountingClient
    {
        DB::beginTransaction();
        try {
            $client = $this->clientRepository->create([
                'company_name' => $dto->companyName,
                'registration_number' => $dto->registrationNumber,
                'tax_number' => $dto->taxNumber,
                'service_package' => $dto->servicePackage,
                'assigned_accountant_id' => $dto->assignedAccountantId,
                'status' => 'pending'
            ]);

            $this->notificationService->sendClientWelcome($client);

            $this->activityLogger->log('accounting_client_created', [
                'client_id' => $client->id,
                'company_name' => $client->company_name
            ]);

            DB::commit();
            return $client;
        } catch (Exception $e) {
            DB::rollback();
            throw new AccountingServiceException('Failed to onboard client');
        }
    }
}
```

#### **Transaction Processing Service**
```php
class TransactionService implements TransactionServiceInterface
{
    public function recordTransaction(TransactionDTO $dto): AccountingTransaction
    {
        $client = $this->clientRepository->findActiveById($dto->clientId);

        $taxCalculation = $this->taxService->calculateTax(
            $dto->amount,
            $dto->taxRate,
            $dto->transactionType
        );

        return $this->transactionRepository->create([
            'client_id' => $client->id,
            'transaction_date' => $dto->transactionDate,
            'reference_number' => $dto->referenceNumber,
            'description' => $dto->description,
            'debit_amount' => $dto->debitAmount,
            'credit_amount' => $dto->creditAmount,
            'transaction_type' => $dto->transactionType,
            'tax_amount' => $taxCalculation['tax_amount'],
            'created_by' => auth()->id()
        ]);
    }
}
```

#### **Security & Compliance Features**
- **Data Encryption**: All financial data encrypted at rest and in transit
- **Audit Trail**: Complete activity logging for all accounting operations
- **Role-based Access**: Strict permissions for accounting data access
- **SARS Compliance**: Built-in South African tax compliance features
- **Backup & Recovery**: Automated daily backups with point-in-time recovery
- **Document Security**: Secure file storage with virus scanning

#### **Naming Conventions**
```php
// Models: Singular, PascalCase
Product, OrderItem, UserAddress

// Controllers: PascalCase + Controller suffix
ProductController, OrderController

// Services: PascalCase + Service suffix
PaymentService, EmailService

// Actions: PascalCase + Action suffix
CreateOrderAction, ProcessPaymentAction

// Variables: camelCase
$productName, $orderTotal, $userEmail

// Constants: UPPER_SNAKE_CASE
ORDER_STATUS_PENDING, PAYMENT_METHOD_STRIPE
```

@extends('layouts.app')

@section('title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('meta_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('meta_keywords', $product->meta_keywords ?: $product->categories->pluck('name')->implode(', '))

@section('og_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('og_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('og_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/og-image.jpg'))

@section('twitter_title', ($product->meta_title ?: $product->name) . ' - ' . __('common.company_name'))
@section('twitter_description', $product->meta_description ?: $product->short_description ?: Str::limit(strip_tags($product->description), 160))
@section('twitter_image', $product->featured_image ? asset('storage/' . $product->featured_image) : asset('images/twitter-image.jpg'))

@push('structured_data')
@verbatim
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Product",
  "name": @json($product->name),
  "description": @json($product->meta_description ?: $product->short_description ?: strip_tags($product->description)),
  "image": [
    @if($product->featured_image)
      @json(asset('storage/' . $product->featured_image))@if($product->gallery && count($product->gallery) > 0),@endif
    @endif
    @if($product->gallery && count($product->gallery) > 0)
      @foreach($product->gallery as $image)
        @json(asset('storage/' . $image))@if(!$loop->last),@endif
      @endforeach
    @endif
  ],
  @if($product->sku)
  "sku": @json($product->sku),
  @endif
  @if($product->brand)
  "brand": {
    "@type": "Brand",
    "name": @json($product->brand)
  },
  @endif
  "offers": {
    "@type": "AggregateOffer",
    "lowPrice": @json($product->price),
    "highPrice": @json($product->price),
    "priceCurrency": "ZAR",
    "availability": @json($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock')
  }
}
</script>
@endverbatim
@endpush

@section('content')
<!-- Product Detail -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div>
                <div class="bg-white rounded-lg shadow-sm p-8">
                    <!-- Main Image -->
                    <div class="mb-4">
                        <img id="main-image" src="{{ $product->featured_image ? asset('storage/' . $product->featured_image) : $product->primary_image }}"
                             alt="{{ $product->name }}"
                             class="w-full h-96 object-cover rounded-lg">
                    </div>

                    <!-- Image Gallery -->
                    @if($product->gallery && count($product->gallery) > 0)
                        <div class="grid grid-cols-4 gap-2">
                            <!-- Featured Image Thumbnail -->
                            @if($product->featured_image)
                                <button type="button"
                                        class="gallery-thumb border-2 border-blue-500 rounded-lg overflow-hidden"
                                        onclick="changeMainImage('{{ asset('storage/' . $product->featured_image) }}', this)">
                                    <img src="{{ asset('storage/' . $product->featured_image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-20 object-cover">
                                </button>
                            @endif

                            <!-- Gallery Images Thumbnails -->
                            @foreach($product->gallery as $image)
                                <button type="button"
                                        class="gallery-thumb border-2 border-gray-200 hover:border-blue-500 rounded-lg overflow-hidden transition-colors"
                                        onclick="changeMainImage('{{ asset('storage/' . $image) }}', this)">
                                    <img src="{{ asset('storage/' . $image) }}"
                                         alt="{{ $product->name }}"
                                         class="w-full h-20 object-cover">
                                </button>
                            @endforeach
                        </div>
                    @endif
                </div>

                <!-- Product Features -->
                @if($product->is_featured)
                    <div class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                            </svg>
                            <span class="text-yellow-800 font-medium">Featured Product</span>
                        </div>
                    </div>
                @endif
            </div>
            
            <!-- Product Info -->
            <div>
                <!-- Breadcrumb -->
                <nav class="mb-4">
                    <ol class="flex items-center space-x-2 text-sm text-gray-500">
                        <li><a href="{{ route('shop.index', ['locale' => app()->getLocale()]) }}" class="hover:text-blue-600">Shop</a></li>
                        @if($product->categories->first())
                            <li><span class="mx-2">/</span></li>
                            <li><a href="{{ route('shop.category', ['category' => $product->categories->first()->slug, 'locale' => app()->getLocale()]) }}" class="hover:text-blue-600">{{ $product->categories->first()->name }}</a></li>
                        @endif
                        <li><span class="mx-2">/</span></li>
                        <li class="text-gray-900">{{ $product->name }}</li>
                    </ol>
                </nav>

                <!-- Categories -->
                <div class="mb-4">
                    @foreach($product->categories as $category)
                    <a href="{{ route('shop.category', ['category' => $category->slug, 'locale' => app()->getLocale()]) }}"
                       class="inline-block px-3 py-1 bg-blue-100 text-blue-600 text-sm rounded-full mr-2 hover:bg-blue-200 transition-colors">
                        {{ $category->name }}
                    </a>
                    @endforeach
                </div>

                <!-- Product Title -->
                <h1 class="heading-1 text-gray-900 mb-4">{{ $product->name }}</h1>

                <!-- Product Meta Info -->
                <div class="flex items-center space-x-4 mb-6 text-sm text-gray-600">
                    @if($product->sku)
                        <span><strong>SKU:</strong> {{ $product->sku }}</span>
                    @endif
                    @if($product->brand)
                        <span><strong>Brand:</strong> {{ $product->brand }}</span>
                    @endif
                    @if($product->model_number)
                        <span><strong>Model:</strong> {{ $product->model_number }}</span>
                    @endif
                </div>
                
                <div class="flex items-center space-x-4 mb-6">
                    <span class="text-3xl font-bold text-gray-900">{{ $product->formatted_price }}</span>
                    @if($product->compare_price)
                    <span class="text-xl text-gray-500 line-through">{{ $product->formatted_compare_price }}</span>
                    <span class="px-2 py-1 bg-red-100 text-red-600 text-sm rounded">
                        Save {{ $product->discount_percentage }}%
                    </span>
                    @endif
                </div>
                
                @if($product->short_description)
                <div class="text-lead text-gray-600 mb-6 prose prose-gray max-w-none">
                    {!! $product->short_description !!}
                </div>
                @endif

                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                    <div class="prose prose-gray max-w-none">
                        {!! $product->description !!}
                    </div>
                </div>
                
                <!-- Add to Cart Form -->
                <form id="add-to-cart-form" class="space-y-6">
                    @csrf
                    <input type="hidden" name="product_id" value="{{ $product->id }}">
                    
                    @if($product->variants->count() > 0)
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Option</label>
                        <select name="variant_id" class="form-input">
                            @foreach($product->variants as $variant)
                            <option value="{{ $variant->id }}" {{ !$variant->isInStock() ? 'disabled' : '' }}>
                                {{ $variant->name }} - {{ $variant->formatted_price }}
                                @if(!$variant->isInStock()) (Out of Stock) @endif
                            </option>
                            @endforeach
                        </select>
                    </div>
                    @endif
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                        <div class="flex items-center space-x-3">
                            <button type="button" class="quantity-btn decrease-qty px-3 py-2 border border-gray-300 rounded-l-lg hover:bg-gray-50">-</button>
                            <input type="number" name="quantity" value="1" min="1" max="10" 
                                   class="quantity-input w-20 px-3 py-2 border-t border-b border-gray-300 text-center focus:ring-0 focus:border-gray-300">
                            <button type="button" class="quantity-btn increase-qty px-3 py-2 border border-gray-300 rounded-r-lg hover:bg-gray-50">+</button>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        @if($product->isInStock())
                        <button type="submit" class="w-full btn-primary">
                            Add to Cart
                            <svg class="ml-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                            </svg>
                        </button>
                        @else
                        <div class="w-full bg-gray-300 text-gray-500 px-6 py-3 rounded-lg text-center">
                            Out of Stock
                        </div>
                        @endif
                        
                        <div class="text-center">
                            <span class="text-sm text-gray-500">
                                Stock Status: <span class="font-medium">{{ $product->stock_status }}</span>
                            </span>
                        </div>
                    </div>
                </form>
                
                <!-- Product Details -->
                <div class="mt-8 pt-8 border-t border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
                    <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        @if($product->sku)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">SKU</dt>
                                <dd class="text-sm text-gray-900 font-mono bg-gray-50 px-2 py-1 rounded">{{ $product->sku }}</dd>
                            </div>
                        @endif

                        @if($product->brand)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Brand</dt>
                                <dd class="text-sm text-gray-900">{{ $product->brand }}</dd>
                            </div>
                        @endif

                        @if($product->model_number)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Model</dt>
                                <dd class="text-sm text-gray-900">{{ $product->model_number }}</dd>
                            </div>
                        @endif

                        @if($product->barcode)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Barcode</dt>
                                <dd class="text-sm text-gray-900 font-mono">{{ $product->barcode }}</dd>
                            </div>
                        @endif

                        @if($product->weight)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Weight</dt>
                                <dd class="text-sm text-gray-900">{{ $product->weight }}g</dd>
                            </div>
                        @endif

                        @if($product->dimensions && ($product->dimensions['length'] || $product->dimensions['width'] || $product->dimensions['height']))
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Dimensions (L×W×H)</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $product->dimensions['length'] ?? '0' }} ×
                                    {{ $product->dimensions['width'] ?? '0' }} ×
                                    {{ $product->dimensions['height'] ?? '0' }} cm
                                </dd>
                            </div>
                        @endif

                        @if($product->track_inventory)
                            <div class="flex flex-col">
                                <dt class="text-sm font-medium text-gray-500 mb-1">Stock Quantity</dt>
                                <dd class="text-sm text-gray-900">
                                    {{ $product->inventory_quantity }} units
                                    @if($product->inventory_quantity <= $product->low_stock_threshold)
                                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            Low Stock
                                        </span>
                                    @endif
                                </dd>
                            </div>
                        @endif

                        <div class="flex flex-col md:col-span-2">
                            <dt class="text-sm font-medium text-gray-500 mb-1">Categories</dt>
                            <dd class="text-sm text-gray-900">
                                @foreach($product->categories as $category)
                                    <a href="{{ route('shop.category', ['category' => $category->slug, 'locale' => app()->getLocale()]) }}"
                                       class="inline-block px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded mr-1 mb-1 hover:bg-blue-200 transition-colors">
                                        {{ $category->name }}
                                    </a>
                                @endforeach
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Product Reviews Section -->
        <div class="mt-20">
            <div class="max-w-4xl mx-auto">
                <!-- Reviews Header with Rating Summary -->
                <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Customer Reviews</h2>
                        @if($product->review_count > 0)
                            <div class="text-right">
                                <div class="flex items-center space-x-2">
                                    <div class="text-3xl font-bold text-yellow-500">{{ $product->formatted_average_rating }}</div>
                                    <div class="text-yellow-400 text-xl">{{ $product->star_rating }}</div>
                                </div>
                                <div class="text-sm text-gray-600">{{ $product->review_count }} {{ Str::plural('review', $product->review_count) }}</div>
                            </div>
                        @endif
                    </div>

                    @if($product->review_count > 0)
                        <!-- Rating Distribution -->
                        <div class="space-y-2">
                            @foreach(array_reverse($product->rating_distribution, true) as $stars => $count)
                                <div class="flex items-center space-x-3">
                                    <div class="flex items-center space-x-1 w-16">
                                        <span class="text-sm font-medium">{{ $stars }}</span>
                                        <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                        </svg>
                                    </div>
                                    <div class="flex-1 bg-gray-200 rounded-full h-2">
                                        <div class="bg-yellow-400 h-2 rounded-full" style="width: {{ $product->review_count > 0 ? ($count / $product->review_count) * 100 : 0 }}%"></div>
                                    </div>
                                    <div class="text-sm text-gray-600 w-12">{{ $count }}</div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">No Reviews Yet</h3>
                            <p class="text-gray-600">Be the first to review this product!</p>
                        </div>
                    @endif
                </div>

                <!-- Review Form -->
                @auth
                    @if($userEligibility && $userEligibility['eligible'])
                        <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                            @if($userReview)
                                <h3 class="text-xl font-bold text-gray-900 mb-6">Update Your Review</h3>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-blue-800">You previously rated this product {{ $userReview->rating }} stars. You can update your review below.</span>
                                    </div>
                                </div>
                            @else
                                <h3 class="text-xl font-bold text-gray-900 mb-6">Write a Review</h3>
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                    <div class="flex items-center space-x-2">
                                        <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span class="text-sm text-green-800">✓ Verified Purchase - You can review this product</span>
                                    </div>
                                </div>
                            @endif

                            <form id="review-form" data-product-slug="{{ $product->slug }}" data-review-id="{{ $userReview ? $userReview->uuid : '' }}">
                                @csrf

                                <!-- Order Selection -->
                                @if($userEligibility['orders']->count() > 1)
                                    <div class="mb-6">
                                        <label for="order_id" class="block text-sm font-medium text-gray-700 mb-2">Select Order</label>
                                        <select id="order_id" name="order_id" class="form-select w-full border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500" required>
                                            <option value="">Choose the order for this review</option>
                                            @foreach($userEligibility['orders'] as $order)
                                                <option value="{{ $order->id }}" {{ $userReview && $userReview->order_id == $order->id ? 'selected' : '' }}>
                                                    Order #{{ $order->order_number }} - {{ $order->created_at->format('M j, Y') }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                @else
                                    <input type="hidden" name="order_id" value="{{ $userEligibility['orders']->first()->id }}">
                                @endif

                                <!-- Rating -->
                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Your Rating</label>
                                    <div class="flex items-center space-x-1" id="rating-stars">
                                        @for($i = 1; $i <= 5; $i++)
                                            <button type="button"
                                                    class="rating-star text-3xl {{ $userReview && $userReview->rating >= $i ? 'text-yellow-400' : 'text-gray-300' }} hover:text-yellow-400 transition-colors"
                                                    data-rating="{{ $i }}">
                                                ★
                                            </button>
                                        @endfor
                                    </div>
                                    <input type="hidden" name="rating" id="rating-input" value="{{ $userReview ? $userReview->rating : '' }}" required>
                                    <p class="text-xs text-gray-500 mt-1">Click on stars to rate this product</p>
                                </div>

                                <!-- Review Content -->
                                <div class="mb-6">
                                    <label for="review_content" class="block text-sm font-medium text-gray-700 mb-2">Your Review</label>
                                    <textarea id="review_content"
                                              name="review_content"
                                              rows="5"
                                              class="form-textarea w-full border-gray-300 rounded-lg focus:border-blue-500 focus:ring-blue-500"
                                              placeholder="Share your experience with this product..."
                                              required
                                              minlength="10"
                                              maxlength="1000">{{ $userReview ? $userReview->review_content : '' }}</textarea>
                                    <p class="text-xs text-gray-500 mt-1">Minimum 10 characters, maximum 1000 characters</p>
                                </div>

                                <!-- Submit Button -->
                                <div class="flex justify-end">
                                    <button type="submit" id="submit-review" class="btn-primary">
                                        <span class="submit-text">{{ $userReview ? 'Update Review' : 'Submit Review' }}</span>
                                        <span class="loading-text hidden">{{ $userReview ? 'Updating...' : 'Submitting...' }}</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    @else
                        <div class="bg-gray-50 rounded-lg p-8 mb-8 text-center">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Purchase Required to Review</h3>
                            <p class="text-gray-600 mb-4">{{ $userEligibility ? $userEligibility['reason'] : 'You must purchase and receive this product before you can leave a review.' }}</p>
                            <a href="{{ route('cart.add', ['product' => $product->slug, 'locale' => app()->getLocale()]) }}" class="btn-primary">
                                Add to Cart
                            </a>
                        </div>
                    @endif
                @else
                    <div class="bg-gray-50 rounded-lg p-8 mb-8 text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Login to Review</h3>
                        <p class="text-gray-600 mb-4">Please log in to leave a review for this product.</p>
                        <div class="flex justify-center space-x-4">
                            <a href="{{ route('login') }}" class="btn-primary">Login</a>
                            <a href="{{ route('register') }}" class="btn-secondary">Register</a>
                        </div>
                    </div>
                @endauth

                <!-- Reviews Display -->
                @if($reviews->count() > 0)
                    <div class="space-y-6">
                        @foreach($reviews as $review)
                            <div class="bg-white rounded-lg shadow-lg p-6">
                                <!-- Review Header -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-start space-x-4">
                                        <!-- User Avatar -->
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-semibold">
                                                    {{ substr($review->user->first_name, 0, 1) }}{{ substr($review->user->last_name, 0, 1) }}
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Review Info -->
                                        <div class="flex-1">
                                            <div class="flex items-center space-x-2 mb-2">
                                                <h4 class="text-lg font-semibold text-gray-900">
                                                    {{ $review->user->first_name }} {{ $review->user->last_name }}
                                                </h4>
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                    ✓ Verified Purchase
                                                </span>
                                                @if($review->is_recent)
                                                    <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                                                        Recent
                                                    </span>
                                                @endif
                                            </div>

                                            <div class="flex items-center space-x-3 mb-2">
                                                <div class="text-yellow-400 text-lg">{{ $review->star_rating }}</div>
                                                <span class="text-sm text-gray-600">{{ $review->time_ago }}</span>
                                                @if($review->days_since_purchase)
                                                    <span class="text-xs text-gray-500">
                                                        ({{ $review->days_since_purchase }} days after purchase)
                                                    </span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Review Actions -->
                                    @auth
                                        <div class="flex items-center space-x-2">
                                            <button type="button"
                                                    class="helpful-btn text-sm text-gray-500 hover:text-blue-600 transition-colors flex items-center space-x-1"
                                                    data-review-id="{{ $review->uuid }}"
                                                    data-is-helpful="{{ $review->isMarkedHelpfulBy(Auth::id()) ? 'true' : 'false' }}">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v4M9 7H7l-1.5-1.5M9 7v10"></path>
                                                </svg>
                                                <span class="helpful-count">{{ $review->helpful_count }}</span>
                                            </button>

                                            <button type="button"
                                                    class="flag-btn text-sm text-gray-500 hover:text-red-600 transition-colors"
                                                    data-review-id="{{ $review->uuid }}">
                                                Flag
                                            </button>
                                        </div>
                                    @endauth
                                </div>

                                <!-- Review Content -->
                                <div class="prose prose-sm max-w-none mb-4">
                                    {!! nl2br(e($review->review_content)) !!}
                                </div>

                                <!-- Review Footer -->
                                @if($review->helpful_count > 0)
                                    <div class="text-xs text-gray-500 border-t border-gray-200 pt-3">
                                        {{ $review->helpful_count }} {{ Str::plural('person', $review->helpful_count) }} found this review helpful
                                    </div>
                                @endif
                            </div>
                        @endforeach

                        <!-- Pagination -->
                        @if($reviews->hasPages())
                            <div class="mt-8">
                                {{ $reviews->links() }}
                            </div>
                        @endif
                    </div>
                @endif
            </div>
        </div>

        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-20">
            <h2 class="heading-2 text-center mb-12">
                Related <span class="text-blue-600">Products</span>
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                @foreach($relatedProducts as $relatedProduct)
                <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                    <div class="relative">
                        <a href="{{ route('shop.product', ['product' => $relatedProduct->slug, 'locale' => app()->getLocale()]) }}">
                            <img src="{{ $relatedProduct->primary_image }}" alt="{{ $relatedProduct->name }}"
                                 class="w-full h-48 object-cover">
                        </a>
                        
                        @if($relatedProduct->discount_percentage > 0)
                        <div class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                            -{{ $relatedProduct->discount_percentage }}%
                        </div>
                        @endif
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">
                            <a href="{{ route('shop.product', ['product' => $relatedProduct->slug, 'locale' => app()->getLocale()]) }}" class="hover:text-blue-600 transition-colors">
                                {{ $relatedProduct->name }}
                            </a>
                        </h3>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-lg font-bold text-gray-900">{{ $relatedProduct->formatted_price }}</span>
                                @if($relatedProduct->compare_price)
                                <span class="text-sm text-gray-500 line-through">{{ $relatedProduct->formatted_compare_price }}</span>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</section>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Quantity controls
    const quantityInput = document.querySelector('.quantity-input');
    const decreaseBtn = document.querySelector('.decrease-qty');
    const increaseBtn = document.querySelector('.increase-qty');
    
    decreaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue > 1) {
            quantityInput.value = currentValue - 1;
        }
    });
    
    increaseBtn.addEventListener('click', function() {
        const currentValue = parseInt(quantityInput.value);
        if (currentValue < 10) {
            quantityInput.value = currentValue + 1;
        }
    });
    
    // Add to cart form
    const addToCartForm = document.getElementById('add-to-cart-form');
    if (addToCartForm) {
        addToCartForm.addEventListener('submit', function(e) {
            e.preventDefault();

            console.log('🛒 Add to cart form submitted');

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;

            // Log form data
            console.log('📝 Form data:', Object.fromEntries(formData));

            submitButton.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Adding...
            `;
            submitButton.disabled = true;

            console.log('🚀 Sending request to {{ route('cart.add', ['locale' => app()->getLocale()]) }}');

            fetch('{{ route('cart.add', ['locale' => app()->getLocale()]) }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: formData
            })
            .then(response => {
                console.log('📡 Response received:', response.status, response.statusText);
                return response.json();
            })
            .then(data => {
                console.log('📦 Response data:', data);
                if (data.success) {
                    showNotification(data.message, 'success');

                    // Update cart count in header
                    updateCartCount(data.cart_count || data.cart.item_count);

                    // Change button to "View Cart"
                    submitButton.innerHTML = `
                        <svg class="mr-2 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                        View Cart
                    `;
                    submitButton.disabled = false;

                    // Change button behavior to redirect to cart
                    submitButton.onclick = function(e) {
                        e.preventDefault();
                        window.location.href = '{{ route('cart.index', ['locale' => app()->getLocale()]) }}';
                    };

                    // Remove form submit listener to prevent double submission
                    addToCartForm.removeEventListener('submit', arguments.callee);
                } else {
                    console.error('❌ Cart add failed:', data.message);
                    showNotification(data.message, 'error');
                    submitButton.innerHTML = originalText;
                    submitButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('💥 Cart add error:', error);
                showNotification('An error occurred. Please try again.', 'error');
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            });
        });
    }
});

// Note: updateCartCount and showNotification functions are now loaded globally from cart-utils.js
</script>
@endpush
@endsection

@push('structured_data')
@verbatim
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Product",
    "name": @json($product->name),
    "description": @json($product->meta_description ?: $product->short_description ?: strip_tags($product->description)),
    "image": [
        @if($product->featured_image)
            @json(asset('storage/' . $product->featured_image))@if($product->gallery && count($product->gallery) > 0),@endif
        @endif
        @if($product->gallery && count($product->gallery) > 0)
            @foreach($product->gallery as $image)
                @json(asset('storage/' . $image))@if(!$loop->last),@endif
            @endforeach
        @endif
    ],
    @if($product->sku)
    "sku": @json($product->sku),
    @endif
    @if($product->brand)
    "brand": {
        "@type": "Brand",
        "name": @json($product->brand)
    },
    @endif
    @if($product->model_number)
    "model": "{{ $product->model_number }}",
    @endif
    "offers": {
        "@type": "Offer",
        "url": "{{ route('shop.product', ['product' => $product->slug, 'locale' => app()->getLocale()]) }}",
        "priceCurrency": "ZAR",
        "price": "{{ $product->price }}",
        @if($product->compare_price)
        "priceValidUntil": "{{ now()->addYear()->format('Y-m-d') }}",
        @endif
        "availability": "{{ $product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock' }}",
        "seller": {
            "@type": "Organization",
            "name": "{{ __('common.company_name') }}"
        }
    },
    "category": "{{ $product->categories->first()->name ?? 'Products' }}",
    @if($product->weight)
    "weight": {
        "@type": "QuantitativeValue",
        "value": "{{ $product->weight }}",
        "unitCode": "GRM"
    },
    @endif
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "5",
        "reviewCount": "1"
    }
}
</script>
@endverbatim
@endpush

@push('scripts')
<script>
// Image gallery functionality
function changeMainImage(imageSrc, thumbElement) {
    document.getElementById('main-image').src = imageSrc;

    // Update thumbnail borders
    document.querySelectorAll('.gallery-thumb').forEach(thumb => {
        thumb.classList.remove('border-blue-500');
        thumb.classList.add('border-gray-200');
    });

    thumbElement.classList.remove('border-gray-200');
    thumbElement.classList.add('border-blue-500');
}

// Quantity controls


// Notification function
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    const bgColor = {
        'success': 'bg-green-500',
        'error': 'bg-red-500',
        'warning': 'bg-yellow-500',
        'info': 'bg-blue-500'
    }[type] || 'bg-blue-500';

    notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform transition-transform duration-300 translate-x-full`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Slide in
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // Slide out and remove
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Product Review System JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Rating stars functionality
    const ratingStars = document.querySelectorAll('.rating-star');
    const ratingInput = document.getElementById('rating-input');

    ratingStars.forEach(star => {
        star.addEventListener('click', function() {
            const rating = parseInt(this.dataset.rating);
            ratingInput.value = rating;

            // Update star display
            ratingStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.remove('text-gray-300');
                    s.classList.add('text-yellow-400');
                } else {
                    s.classList.remove('text-yellow-400');
                    s.classList.add('text-gray-300');
                }
            });
        });

        star.addEventListener('mouseenter', function() {
            const rating = parseInt(this.dataset.rating);
            ratingStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('text-yellow-300');
                } else {
                    s.classList.remove('text-yellow-300');
                }
            });
        });
    });

    // Review form submission
    const reviewForm = document.getElementById('review-form');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitReview();
        });
    }

    // Helpful button functionality
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('helpful-btn') || e.target.closest('.helpful-btn')) {
            const button = e.target.classList.contains('helpful-btn') ? e.target : e.target.closest('.helpful-btn');
            toggleHelpful(button);
        }

        if (e.target.classList.contains('flag-btn')) {
            flagReview(e.target);
        }
    });
});

function submitReview() {
    const form = document.getElementById('review-form');
    const submitBtn = document.getElementById('submit-review');
    const submitText = submitBtn.querySelector('.submit-text');
    const loadingText = submitBtn.querySelector('.loading-text');

    // Show loading state
    submitBtn.disabled = true;
    submitText.classList.add('hidden');
    loadingText.classList.remove('hidden');

    const formData = new FormData(form);
    const productSlug = form.dataset.productSlug;
    const reviewId = form.dataset.reviewId;

    const url = reviewId ?
        `/products/${productSlug}/reviews/${reviewId}` :
        `/products/${productSlug}/reviews`;
    const method = reviewId ? 'PUT' : 'POST';

    // Convert FormData to regular object for PUT requests
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');

            // Reload the page to show the new/updated review
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showNotification(data.message, 'error');
            if (data.errors) {
                console.error('Validation errors:', data.errors);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred while submitting your review.', 'error');
    })
    .finally(() => {
        // Reset button state
        submitBtn.disabled = false;
        submitText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    });
}

function toggleHelpful(button) {
    const reviewId = button.dataset.reviewId;
    const isHelpful = button.dataset.isHelpful === 'true';
    const method = isHelpful ? 'DELETE' : 'POST';
    const url = `/reviews/${reviewId}/helpful`;

    fetch(url, {
        method: method,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            button.dataset.isHelpful = isHelpful ? 'false' : 'true';
            button.querySelector('.helpful-count').textContent = data.helpful_count;

            if (isHelpful) {
                button.classList.remove('text-blue-600');
                button.classList.add('text-gray-500');
            } else {
                button.classList.remove('text-gray-500');
                button.classList.add('text-blue-600');
            }

            showNotification(data.message, 'success');
        } else {
            showNotification(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred.', 'error');
    });
}

function flagReview(button) {
    const reviewId = button.dataset.reviewId;

    if (confirm('Are you sure you want to flag this review as inappropriate?')) {
        fetch(`/reviews/${reviewId}/flag`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            showNotification(data.message, data.success ? 'success' : 'error');
            if (data.success) {
                button.disabled = true;
                button.textContent = 'Flagged';
                button.classList.add('text-red-600');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('An error occurred.', 'error');
        });
    }
}
</script>
@endpush

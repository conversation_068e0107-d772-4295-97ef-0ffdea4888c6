<?php $__env->startSection('title', 'Orders Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="p-6 space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Orders Management</h1>
            <p class="text-gray-600">Manage customer orders and track fulfillment</p>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <form method="GET" action="<?php echo e(route('admin.orders.index')); ?>" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Order number, email, customer name..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Order Status</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="all" <?php echo e(request('status') === 'all' ? 'selected' : ''); ?>>All Statuses</option>
                        <option value="pending" <?php echo e(request('status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="processing" <?php echo e(request('status') === 'processing' ? 'selected' : ''); ?>>Processing</option>
                        <option value="shipped" <?php echo e(request('status') === 'shipped' ? 'selected' : ''); ?>>Shipped</option>
                        <option value="delivered" <?php echo e(request('status') === 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                        <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                    </select>
                </div>

                <!-- Payment Status Filter -->
                <div>
                    <label for="payment_status" class="block text-sm font-medium text-gray-700 mb-1">Payment Status</label>
                    <select id="payment_status" name="payment_status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="all" <?php echo e(request('payment_status') === 'all' ? 'selected' : ''); ?>>All Payment Statuses</option>
                        <option value="pending" <?php echo e(request('payment_status') === 'pending' ? 'selected' : ''); ?>>Pending</option>
                        <option value="paid" <?php echo e(request('payment_status') === 'paid' ? 'selected' : ''); ?>>Paid</option>
                        <option value="failed" <?php echo e(request('payment_status') === 'failed' ? 'selected' : ''); ?>>Failed</option>
                        <option value="refunded" <?php echo e(request('payment_status') === 'refunded' ? 'selected' : ''); ?>>Refunded</option>
                    </select>
                </div>

                <!-- Sort -->
                <div>
                    <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select id="sort_by" name="sort_by" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Date Created</option>
                        <option value="order_number" <?php echo e(request('sort_by') === 'order_number' ? 'selected' : ''); ?>>Order Number</option>
                        <option value="total_amount" <?php echo e(request('sort_by') === 'total_amount' ? 'selected' : ''); ?>>Total Amount</option>
                        <option value="status" <?php echo e(request('sort_by') === 'status' ? 'selected' : ''); ?>>Status</option>
                    </select>
                    <input type="hidden" name="sort_direction" value="<?php echo e(request('sort_direction', 'desc')); ?>">
                </div>
            </div>

            <div class="flex justify-between items-center">
                <button type="submit" class="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                    Apply Filters
                </button>
                
                <?php if(request()->hasAny(['search', 'status', 'payment_status', 'sort_by'])): ?>
                    <a href="<?php echo e(route('admin.orders.index')); ?>" class="text-gray-600 hover:text-gray-800">
                        Clear Filters
                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <!-- Status Tabs -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'all']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status', 'all') === 'all' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    All Orders
                    <span class="bg-gray-100 text-gray-900 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['all']); ?></span>
                </a>
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'pending']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') === 'pending' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    Pending
                    <span class="bg-yellow-100 text-yellow-800 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['pending']); ?></span>
                </a>
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'processing']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') === 'processing' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    Processing
                    <span class="bg-blue-100 text-blue-800 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['processing']); ?></span>
                </a>
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'shipped']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') === 'shipped' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    Shipped
                    <span class="bg-purple-100 text-purple-800 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['shipped']); ?></span>
                </a>
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'delivered']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') === 'delivered' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    Delivered
                    <span class="bg-green-100 text-green-800 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['delivered']); ?></span>
                </a>
                <a href="<?php echo e(route('admin.orders.index', array_merge(request()->except('status'), ['status' => 'cancelled']))); ?>" 
                   class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm <?php echo e(request('status') === 'cancelled' ? 'border-primary-500 text-primary-600' : ''); ?>">
                    Cancelled
                    <span class="bg-red-100 text-red-800 ml-2 py-0.5 px-2.5 rounded-full text-xs font-medium"><?php echo e($statusCounts['cancelled']); ?></span>
                </a>
            </nav>
        </div>

        <!-- Orders Table -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Order
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Payment
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Total
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">Actions</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php $__empty_1 = true; $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <?php echo $__env->make('admin.orders.partials.order-row', ['order' => $order], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center text-gray-500">
                                <div class="flex flex-col items-center">
                                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                    </svg>
                                    <p class="text-lg font-medium">No orders found</p>
                                    <p class="text-sm">Try adjusting your search or filter criteria.</p>
                                </div>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <?php if($orders->hasPages()): ?>
            <div class="px-6 py-4 border-t border-gray-200">
                <?php echo e($orders->links()); ?>

            </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle status updates
    document.querySelectorAll('.status-select').forEach(function(select) {
        select.addEventListener('change', function() {
            const orderId = this.dataset.orderId;
            const newStatus = this.value;
            const currentStatus = this.dataset.currentStatus;

            if (newStatus === currentStatus) {
                return;
            }

            // Show loading state
            this.disabled = true;

            fetch(`/admin/orders/${orderId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    status: newStatus
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the status badge
                    const statusContainer = document.querySelector(`[data-order-id="${orderId}"] .status-container`);
                    if (statusContainer) {
                        statusContainer.innerHTML = data.status_badge;
                    }

                    // Update current status
                    this.dataset.currentStatus = newStatus;

                    // Show success message
                    showNotification('Order status updated successfully', 'success');
                } else {
                    // Revert selection
                    this.value = currentStatus;
                    showNotification('Failed to update order status', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.value = currentStatus;
                showNotification('An error occurred while updating the order status', 'error');
            })
            .finally(() => {
                this.disabled = false;
            });
        });
    });
});

function showNotification(message, type) {
    // Simple notification - you can enhance this with a proper notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-4 py-2 rounded-md text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\web_devs\chisolution\resources\views/admin/orders/index.blade.php ENDPATH**/ ?>
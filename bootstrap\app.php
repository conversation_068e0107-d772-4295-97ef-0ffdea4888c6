<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withProviders([
        App\Providers\ImageServiceProvider::class,
        App\Providers\FileServiceProvider::class,
        App\Providers\ViewServiceProvider::class,
        App\Providers\LocalizationServiceProvider::class,
        App\Providers\PerformanceServiceProvider::class,
    ])
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(prepend: [
            \App\Http\Middleware\StaticAssetBypassMiddleware::class,
        ]);

        $middleware->web(append: [
            \App\Http\Middleware\SafeOutputMiddleware::class,
            \App\Http\Middleware\LocalizationMiddleware::class,
            \App\Http\Middleware\VisitorAnalyticsMiddleware::class,
        ]);

        $middleware->alias([
            'auth' => \App\Http\Middleware\Authenticate::class,
            'guest' => \App\Http\Middleware\RedirectIfAuthenticated::class,
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'permission' => \App\Http\Middleware\PermissionMiddleware::class,
            'localization' => \App\Http\Middleware\LocalizationMiddleware::class,
            'auto.localize' => \App\Http\Middleware\AutoLocalizeMiddleware::class,
            'activity.log' => \App\Http\Middleware\ActivityLoggingMiddleware::class,
            'visitor.analytics' => \App\Http\Middleware\VisitorAnalyticsMiddleware::class,
            'checkout.error' => \App\Http\Middleware\CheckoutErrorHandler::class,
            'safe.output' => \App\Http\Middleware\SafeOutputMiddleware::class,
            'static.bypass' => \App\Http\Middleware\StaticAssetBypassMiddleware::class,
            'chat.auth' => \App\Http\Middleware\ChatApiAuthentication::class,
            'chat.usage' => \App\Http\Middleware\ChatApiUsageTracking::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle 404 errors with activity logging
        $exceptions->render(function (\Symfony\Component\HttpKernel\Exception\HttpException $e, $request) {
            if ($e->getStatusCode() === 404) {
                // Log 404 errors for debugging
                app(\App\Services\ActivityLogger::class)->logActivity(
                    'error_404',
                    '404 Not Found: ' . $request->fullUrl(),
                    'error',
                    'Page not found',
                    [
                        'url' => $request->fullUrl(),
                        'method' => $request->method(),
                        'user_agent' => $request->userAgent(),
                        'ip' => $request->ip(),
                    ]
                );
            }

            // Handle CSRF token mismatch with user-friendly message
            if ($e->getStatusCode() === 419) {
                $message = 'CSRF token mismatch. Please refresh the page and try again.';

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message,
                        'error' => [
                            'code' => 'CSRF_TOKEN_MISMATCH',
                            'message' => $message,
                            'action' => 'refresh_page',
                            'timestamp' => now()->toISOString()
                        ]
                    ], 419);
                }

                // For non-AJAX requests, redirect back with error
                return redirect()->back()
                    ->withInput()
                    ->withErrors(['csrf' => $message]);
            }
        });
    })->create();

<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CheckoutLogController;
use App\Http\Controllers\CartController;
use App\Http\Controllers\Api\V1\ChatController;
use App\Http\Controllers\Api\V1\ChatFileController;
use App\Http\Controllers\Api\V1\ChatAssignmentController;

// Public authentication routes
Route::prefix('v1')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);

    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('/logout', [AuthController::class, 'logout']);
        Route::get('/user', [AuthController::class, 'user']);
    });
});

// Cart API routes (no CSRF protection, but with activity logging)
Route::prefix('cart')->name('api.cart.')->middleware(['activity.log'])->group(function () {
    Route::post('/add', [CartController::class, 'add'])->name('add');
    Route::get('/count', [CartController::class, 'count'])->name('count');
    Route::get('/', [CartController::class, 'index'])->name('index');
    Route::post('/sync', [CartController::class, 'sync'])->name('sync');
});

// Checkout logging API (no CSRF protection for client-side logging)
Route::post('/log-checkout-activity', [CheckoutLogController::class, 'logActivity'])
    ->middleware('throttle:100,1') // Allow 100 requests per minute
    ->name('api.checkout.log');

// Chat API routes - RESTful design for multi-application support
Route::prefix('v1/chat')->name('api.chat.')->middleware(['activity.log', 'chat.usage'])->group(function () {

    // Public chat endpoints (for anonymous visitors)
    Route::post('/rooms', [ChatController::class, 'store'])->name('rooms.store');
    Route::get('/rooms/{uuid}', [ChatController::class, 'show'])->name('rooms.show');
    Route::post('/rooms/{uuid}/messages', [ChatController::class, 'sendMessage'])->name('messages.store');
    Route::get('/rooms/{uuid}/messages', [ChatController::class, 'getMessages'])->name('messages.index');

    // File handling
    Route::post('/rooms/{uuid}/files', [ChatFileController::class, 'upload'])->name('files.upload');
    Route::get('/files/{token}/download', [ChatFileController::class, 'download'])->name('files.download');
    Route::get('/files/{uuid}/preview', [ChatFileController::class, 'preview'])->name('files.preview');
    Route::get('/files/{uuid}', [ChatFileController::class, 'show'])->name('files.show');
    Route::get('/rooms/{uuid}/files', [ChatFileController::class, 'getRoomFiles'])->name('files.room');

    // Protected endpoints (require authentication)
    Route::middleware('chat.auth')->group(function () {

        // Chat management
        Route::get('/rooms', [ChatController::class, 'index'])->name('rooms.index');
        Route::put('/rooms/{uuid}', [ChatController::class, 'update'])->name('rooms.update');
        Route::delete('/rooms/{uuid}', [ChatController::class, 'destroy'])->name('rooms.destroy');

        // Real-time functionality
        Route::post('/rooms/{uuid}/join', [ChatController::class, 'joinRoom'])->name('rooms.join');
        Route::post('/rooms/{uuid}/leave', [ChatController::class, 'leaveRoom'])->name('rooms.leave');
        Route::post('/rooms/{uuid}/typing', [ChatController::class, 'typing'])->name('rooms.typing');
        Route::post('/rooms/{roomUuid}/messages/{messageUuid}/read', [ChatController::class, 'markAsRead'])->name('messages.read');
        Route::get('/rooms/{uuid}/status', [ChatController::class, 'getRoomStatus'])->name('rooms.status');

        // Statistics and analytics
        Route::get('/statistics', [ChatController::class, 'getStatistics'])->name('statistics');
        Route::get('/files/statistics', [ChatFileController::class, 'getStatistics'])->name('files.statistics');

        // File management
        Route::delete('/files/{uuid}', [ChatFileController::class, 'destroy'])->name('files.destroy');

        // Staff/Admin only endpoints
        Route::middleware('role:staff|admin')->group(function () {

            // Assignment management
            Route::get('/assignments', [ChatAssignmentController::class, 'index'])->name('assignments.index');
            Route::post('/assignments', [ChatAssignmentController::class, 'store'])->name('assignments.store');
            Route::post('/assignments/auto-assign', [ChatAssignmentController::class, 'autoAssign'])->name('assignments.auto');
            Route::post('/assignments/{id}/transfer', [ChatAssignmentController::class, 'transfer'])->name('assignments.transfer');
            Route::post('/assignments/{id}/complete', [ChatAssignmentController::class, 'complete'])->name('assignments.complete');
            Route::get('/assignments/statistics', [ChatAssignmentController::class, 'getStatistics'])->name('assignments.statistics');
            Route::get('/staff/available', [ChatAssignmentController::class, 'getAvailableStaff'])->name('staff.available');

        });

        // AI Provider Integration
        Route::prefix('ai')->name('ai.')->group(function () {
            Route::post('/generate', [App\Http\Controllers\Api\V1\ChatAIController::class, 'generateResponse'])->name('generate');
            Route::post('/sentiment', [App\Http\Controllers\Api\V1\ChatAIController::class, 'analyzeSentiment'])->name('sentiment');
            Route::post('/translate', [App\Http\Controllers\Api\V1\ChatAIController::class, 'translate'])->name('translate');
            Route::post('/summarize', [App\Http\Controllers\Api\V1\ChatAIController::class, 'summarizeConversation'])->name('summarize');
            Route::post('/topics', [App\Http\Controllers\Api\V1\ChatAIController::class, 'extractTopics'])->name('topics');
            Route::get('/providers', [App\Http\Controllers\Api\V1\ChatAIController::class, 'getProviders'])->name('providers');
            Route::get('/usage-stats', [App\Http\Controllers\Api\V1\ChatAIController::class, 'getUsageStats'])->name('usage-stats');
            Route::post('/test', [App\Http\Controllers\Api\V1\ChatAIController::class, 'testProvider'])->name('test');
        });
    });
});

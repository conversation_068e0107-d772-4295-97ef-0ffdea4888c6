<tr class="hover:bg-gray-50">
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex flex-col">
            <div class="text-sm font-medium text-gray-900">
                <a href="<?php echo e(route('admin.orders.show', $order)); ?>" class="hover:text-primary-600">
                    #<?php echo e($order->order_number); ?>

                </a>
            </div>
            <div class="text-sm text-gray-500">
                <?php echo e($order->items->count()); ?> <?php echo e(Str::plural('item', $order->items->count())); ?>

            </div>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex flex-col">
            <?php if($order->user): ?>
                <div class="text-sm font-medium text-gray-900">
                    <?php echo e($order->user->first_name); ?> <?php echo e($order->user->last_name); ?>

                </div>
                <div class="text-sm text-gray-500"><?php echo e($order->user->email); ?></div>
            <?php else: ?>
                <div class="text-sm font-medium text-gray-900">Guest Customer</div>
                <div class="text-sm text-gray-500"><?php echo e($order->email); ?></div>
            <?php endif; ?>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="status-container" data-order-id="<?php echo e($order->uuid); ?>">
            <?php if($order->status === 'pending'): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    Pending
                </span>
            <?php elseif($order->status === 'processing'): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    Processing
                </span>
            <?php elseif($order->status === 'shipped'): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    Shipped
                </span>
            <?php elseif($order->status === 'delivered'): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Delivered
                </span>
            <?php elseif($order->status === 'cancelled'): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    Cancelled
                </span>
            <?php endif; ?>
        </div>
        
        <!-- Quick Status Update Dropdown -->
        <div class="mt-1">
            <select class="status-select text-xs border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                    data-order-id="<?php echo e($order->uuid); ?>"
                    data-current-status="<?php echo e($order->status); ?>">
                <option value="pending" <?php echo e($order->status === 'pending' ? 'selected' : ''); ?>>Pending</option>
                <option value="processing" <?php echo e($order->status === 'processing' ? 'selected' : ''); ?>>Processing</option>
                <option value="shipped" <?php echo e($order->status === 'shipped' ? 'selected' : ''); ?>>Shipped</option>
                <option value="delivered" <?php echo e($order->status === 'delivered' ? 'selected' : ''); ?>>Delivered</option>
                <option value="cancelled" <?php echo e($order->status === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
            </select>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <?php if($order->payment_status === 'pending'): ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Pending
            </span>
        <?php elseif($order->payment_status === 'paid'): ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Paid
            </span>
        <?php elseif($order->payment_status === 'failed'): ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                Failed
            </span>
        <?php elseif($order->payment_status === 'refunded'): ?>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Refunded
            </span>
        <?php endif; ?>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
        <div class="font-medium">
            <?php echo e($order->currency->symbol ?? '$'); ?><?php echo e(number_format($order->total_amount, 2)); ?>

        </div>
        <?php if($order->coupon): ?>
            <div class="text-xs text-green-600">
                Coupon: <?php echo e($order->coupon_code); ?>

            </div>
        <?php endif; ?>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div><?php echo e($order->created_at->format('M j, Y')); ?></div>
        <div class="text-xs"><?php echo e($order->created_at->format('g:i A')); ?></div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <div class="flex items-center space-x-2">
            <a href="<?php echo e(route('admin.orders.show', $order)); ?>" 
               class="text-primary-600 hover:text-primary-900 font-medium">
                View
            </a>
        </div>
    </td>
</tr>
<?php /**PATH C:\Users\<USER>\Desktop\web_devs\chisolution\resources\views/admin/orders/partials/order-row.blade.php ENDPATH**/ ?>
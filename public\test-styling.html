<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Styling Test - ChiSolution</title>
    <link href="/build/assets/app-lk_KKQ-R.css" rel="stylesheet">
    <style>
        .test-section {
            margin: 2rem 0;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        .test-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">ChiSolution Styling Test</h1>
        
        <!-- Container Width Tests -->
        <div class="test-section">
            <h2 class="test-title">Container Width Tests</h2>
            
            <div class="mb-4">
                <h3 class="font-semibold mb-2">Standard Container (Responsive)</h3>
                <div class="container bg-blue-100 p-4 rounded">
                    <p>This container should be responsive with max-widths at different breakpoints.</p>
                </div>
            </div>
            
            <div class="mb-4">
                <h3 class="font-semibold mb-2">Full-Width Container</h3>
                <div class="container-fluid bg-green-100 p-4 rounded">
                    <p>This container should always be full-width regardless of screen size.</p>
                </div>
            </div>
            
            <div class="mb-4">
                <h3 class="font-semibold mb-2">Wide Container (95% width)</h3>
                <div class="container-wide bg-yellow-100 p-4 rounded">
                    <p>This container should be 95% width on larger screens.</p>
                </div>
            </div>
        </div>

        <!-- Button Color Tests -->
        <div class="test-section">
            <h2 class="test-title">Button Color Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">Primary Buttons</h3>
                    <button class="bg-primary-600 text-white px-4 py-2 rounded hover:bg-primary-700 mb-2 block w-full">
                        Primary Button
                    </button>
                    <button class="bg-primary-700 text-white px-4 py-2 rounded hover:bg-primary-800 mb-2 block w-full">
                        Primary Dark
                    </button>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-2">Secondary Buttons</h3>
                    <button class="bg-secondary-600 text-white px-4 py-2 rounded hover:bg-secondary-700 mb-2 block w-full">
                        Secondary Button
                    </button>
                    <button class="bg-secondary-700 text-white px-4 py-2 rounded hover:bg-secondary-800 mb-2 block w-full">
                        Secondary Dark
                    </button>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-2">Accent Buttons</h3>
                    <button class="bg-accent-600 text-white px-4 py-2 rounded hover:bg-accent-700 mb-2 block w-full">
                        Accent Button
                    </button>
                    <button class="bg-accent-700 text-white px-4 py-2 rounded hover:bg-accent-800 mb-2 block w-full">
                        Accent Dark
                    </button>
                </div>
            </div>
        </div>

        <!-- Text Color Tests -->
        <div class="test-section">
            <h2 class="test-title">Text Color Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white p-4 rounded">
                    <h3 class="font-semibold mb-2">Primary Text Colors</h3>
                    <p class="text-primary-600 mb-1">Primary 600 Text</p>
                    <p class="text-primary-700 mb-1">Primary 700 Text</p>
                    <p class="text-primary-800 mb-1">Primary 800 Text</p>
                    <p class="text-primary-900">Primary 900 Text</p>
                </div>
                
                <div class="bg-white p-4 rounded">
                    <h3 class="font-semibold mb-2">Secondary Text Colors</h3>
                    <p class="text-secondary-600 mb-1">Secondary 600 Text</p>
                    <p class="text-secondary-700 mb-1">Secondary 700 Text</p>
                    <p class="text-secondary-800 mb-1">Secondary 800 Text</p>
                </div>
                
                <div class="bg-gray-800 p-4 rounded">
                    <h3 class="font-semibold mb-2 text-white">White Text Test</h3>
                    <p class="text-white mb-1">This should be visible white text</p>
                    <p class="text-gray-200">This should be light gray text</p>
                </div>
            </div>
        </div>

        <!-- Hover Effect Tests -->
        <div class="test-section">
            <h2 class="test-title">Hover Effect Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h3 class="font-semibold mb-2">Navigation-style Links</h3>
                    <a href="#" class="nav-link block p-2 rounded mb-1">Navigation Link 1</a>
                    <a href="#" class="nav-link block p-2 rounded mb-1">Navigation Link 2</a>
                    <a href="#" class="nav-link active block p-2 rounded mb-1">Active Navigation Link</a>
                </div>
                
                <div>
                    <h3 class="font-semibold mb-2">Mobile Navigation Links</h3>
                    <a href="#" class="mobile-nav-link block p-2 rounded mb-1">Mobile Nav Link 1</a>
                    <a href="#" class="mobile-nav-link block p-2 rounded mb-1">Mobile Nav Link 2</a>
                    <a href="#" class="mobile-nav-link active block p-2 rounded mb-1">Active Mobile Nav Link</a>
                </div>
            </div>
        </div>

        <!-- Card Tests -->
        <div class="test-section">
            <h2 class="test-title">Card Component Tests</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="card">
                    <h3 class="font-semibold mb-2">Standard Card</h3>
                    <p class="text-gray-600">This is a standard card component with proper styling.</p>
                </div>
                
                <div class="card-hover">
                    <h3 class="font-semibold mb-2">Hover Card</h3>
                    <p class="text-gray-600">This card should have hover effects when you mouse over it.</p>
                </div>
                
                <div class="dashboard-card">
                    <h3 class="font-semibold mb-2">Dashboard Card</h3>
                    <p class="text-gray-600">This is a dashboard-style card component.</p>
                </div>
            </div>
        </div>

        <!-- Browser Compatibility Note -->
        <div class="test-section bg-blue-50 border-blue-200">
            <h2 class="test-title text-blue-800">Browser Compatibility Test</h2>
            <p class="text-blue-700">
                Test this page in different browsers (Chrome, Firefox, Edge) to verify:
            </p>
            <ul class="list-disc list-inside text-blue-700 mt-2 space-y-1">
                <li>Container widths behave consistently</li>
                <li>Button colors are visible (no white-on-white)</li>
                <li>Hover effects work properly</li>
                <li>Text colors have proper contrast</li>
            </ul>
        </div>
    </div>
</body>
</html>

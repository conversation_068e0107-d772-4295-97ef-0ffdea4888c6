<?php

namespace App\Services;

use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;
use Spatie\SchemaOrg\Schema;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;

class SeoService
{
    protected array $jsonLdSchemas = [];
    protected array $breadcrumbs = [];
    protected ?string $canonicalUrl = null;
    protected array $hreflangUrls = [];
    
    public function __construct()
    {
        // Set default values from config
        $this->setDefaults();
    }

    /**
     * Set default SEO values from configuration
     */
    protected function setDefaults(): void
    {
        SEOMeta::setTitle(config('app.name'));
        SEOMeta::setDescription(__('common.company_description'));
        SEOMeta::addKeyword(explode(',', 'digital agency, web development, mobile apps, e-commerce'));
        
        OpenGraph::setTitle(config('app.name'));
        OpenGraph::setDescription(__('common.company_description'));
        OpenGraph::setType('website');
        OpenGraph::setSiteName(config('app.name'));
        OpenGraph::addImage(asset('images/og-image.jpg'));
        
        TwitterCard::setType('summary_large_image');
        TwitterCard::setTitle(config('app.name'));
        TwitterCard::setDescription(__('common.company_description'));
        TwitterCard::setImage(asset('images/twitter-image.jpg'));
    }

    /**
     * Set page title with automatic suffix
     */
    public function setTitle(string $title, bool $appendSiteName = true): self
    {
        $fullTitle = $appendSiteName ? $title . ' - ' . config('app.name') : $title;
        
        SEOMeta::setTitle($fullTitle);
        OpenGraph::setTitle($title);
        TwitterCard::setTitle($title);
        
        return $this;
    }

    /**
     * Set meta description
     */
    public function setDescription(string $description): self
    {
        SEOMeta::setDescription($description);
        OpenGraph::setDescription($description);
        TwitterCard::setDescription($description);
        
        return $this;
    }

    /**
     * Set meta keywords
     */
    public function setKeywords(array|string $keywords): self
    {
        if (is_string($keywords)) {
            $keywords = explode(',', $keywords);
        }
        
        SEOMeta::setKeywords($keywords);
        
        return $this;
    }

    /**
     * Set canonical URL
     */
    public function setCanonical(string $url): self
    {
        $this->canonicalUrl = $url;
        SEOMeta::setCanonical($url);
        OpenGraph::setUrl($url);
        
        return $this;
    }

    /**
     * Add hreflang URLs for multilingual SEO
     */
    public function addHreflang(string $locale, string $url): self
    {
        $this->hreflangUrls[$locale] = $url;
        
        return $this;
    }

    /**
     * Set Open Graph image
     */
    public function setImage(string $imageUrl, array $properties = []): self
    {
        OpenGraph::addImage($imageUrl, $properties);
        TwitterCard::setImage($imageUrl);
        
        return $this;
    }

    /**
     * Add custom meta tag
     */
    public function addMeta(string $name, string $content, string $type = 'name'): self
    {
        SEOMeta::addMeta($name, $content, $type);
        
        return $this;
    }

    /**
     * Add JSON-LD schema
     */
    public function addSchema($schema): self
    {
        if (is_object($schema) && method_exists($schema, 'toArray')) {
            $this->jsonLdSchemas[] = $schema->toArray();
        } elseif (is_array($schema)) {
            $this->jsonLdSchemas[] = $schema;
        }
        
        return $this;
    }

    /**
     * Add organization schema
     */
    public function addOrganizationSchema(array $data = []): self
    {
        $defaultData = [
            'name' => config('app.name'),
            'url' => url('/'),
            'logo' => asset('images/logo.png'),
            'description' => __('common.company_description'),
            'address' => [
                '@type' => 'PostalAddress',
                'addressCountry' => 'ZA',
                'addressRegion' => 'South Africa'
            ],
            'sameAs' => [
                'https://facebook.com/chisolution',
                'https://twitter.com/chisolution',
                'https://linkedin.com/company/chisolution'
            ]
        ];

        $schema = Schema::organization()
            ->name($data['name'] ?? $defaultData['name'])
            ->url($data['url'] ?? $defaultData['url'])
            ->logo($data['logo'] ?? $defaultData['logo'])
            ->description($data['description'] ?? $defaultData['description']);

        if (isset($data['address']) || isset($defaultData['address'])) {
            $address = $data['address'] ?? $defaultData['address'];
            $schema->address(
                Schema::postalAddress()
                    ->addressCountry($address['addressCountry'] ?? 'ZA')
                    ->addressRegion($address['addressRegion'] ?? 'South Africa')
            );
        }

        if (isset($data['sameAs']) || isset($defaultData['sameAs'])) {
            $schema->sameAs($data['sameAs'] ?? $defaultData['sameAs']);
        }

        $this->addSchema($schema);
        
        return $this;
    }

    /**
     * Add breadcrumb schema
     */
    public function addBreadcrumbs(array $breadcrumbs): self
    {
        $this->breadcrumbs = $breadcrumbs;
        
        $listItems = [];
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $listItems[] = Schema::listItem()
                ->position($index + 1)
                ->name($breadcrumb['name'])
                ->item($breadcrumb['url'] ?? null);
        }

        $schema = Schema::breadcrumbList()->itemListElement($listItems);
        $this->addSchema($schema);
        
        return $this;
    }

    /**
     * Generate all SEO tags
     */
    public function render(): string
    {
        $output = [];
        
        // Basic meta tags
        $output[] = SEOMeta::generate();
        $output[] = OpenGraph::generate();
        $output[] = TwitterCard::generate();
        
        // Canonical URL
        if ($this->canonicalUrl) {
            $output[] = '<link rel="canonical" href="' . $this->canonicalUrl . '">';
        }
        
        // Hreflang tags
        foreach ($this->hreflangUrls as $locale => $url) {
            $output[] = '<link rel="alternate" hreflang="' . $locale . '" href="' . $url . '">';
        }
        
        // JSON-LD schemas
        if (!empty($this->jsonLdSchemas)) {
            foreach ($this->jsonLdSchemas as $schema) {
                $output[] = '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE) . '</script>';
            }
        }
        
        return implode("\n", $output);
    }

    /**
     * Get structured data for validation
     */
    public function getStructuredData(): array
    {
        return $this->jsonLdSchemas;
    }

    /**
     * Cache SEO data for performance
     */
    public function cache(string $key, int $minutes = 60): self
    {
        Cache::put("seo.{$key}", $this->jsonLdSchemas, now()->addMinutes($minutes));
        
        return $this;
    }

    /**
     * Load cached SEO data
     */
    public function loadFromCache(string $key): self
    {
        $cached = Cache::get("seo.{$key}");
        if ($cached) {
            $this->jsonLdSchemas = array_merge($this->jsonLdSchemas, $cached);
        }
        
        return $this;
    }
}

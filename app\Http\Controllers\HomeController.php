<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Project;
use App\Models\Service;
use App\Models\BlogPost;
use App\Models\ContactSubmission;
use App\Models\Client;
use App\Services\FileService;
use App\Services\ImageService;
use App\Services\VisitorAnalytics;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Log;

class HomeController extends Controller
{
    protected VisitorAnalytics $visitorAnalytics;

    public function __construct(VisitorAnalytics $visitorAnalytics)
    {
        $this->visitorAnalytics = $visitorAnalytics;
    }

    /**
     * Show the application homepage.
     */
    public function index(): View
    {
        // Track homepage visit with journey step
        $this->visitorAnalytics->trackJourneyStep(
            'Homepage Visit',
            'landing',
            ['page' => 'home', 'is_landing' => true]
        );

        // Get featured products
        $featuredProducts = Product::active()
            ->featured()
            ->with(['categories', 'variants'])
            ->limit(8)
            ->get();

        // Get featured services
        $featuredServices = Service::active()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        // Get featured projects (all for carousel)
        $featuredProjects = Project::where('is_published', true)
            ->where('is_featured', true)
            ->where('is_deleted', false)
            ->with(['client', 'service'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Get latest blog posts
        try {
            if (class_exists(\App\Models\BlogPost::class)) {
                $latestBlogPosts = \App\Models\BlogPost::where('is_published', true)
                    ->where('is_deleted', false)
                    ->with(['category', 'author'])
                    ->orderBy('published_at', 'desc')
                    ->limit(3)
                    ->get();
            } else {
                $latestBlogPosts = collect(); // Empty collection if BlogPost model doesn't exist
            }
        } catch (\Exception $e) {
            $latestBlogPosts = collect(); // Fallback to empty collection on any error
        }

        // Get featured clients for 3D carousel (with error handling)
        try {
            $featuredClients = Client::active()
                ->featured()
                ->ordered()
                ->limit(10)
                ->get();
        } catch (\Exception $e) {
            // If clients table doesn't exist or there's an error, use empty collection
            $featuredClients = collect([]);
        }

        return view('pages.home', compact(
            'featuredProducts',
            'featuredServices',
            'featuredProjects',
            'latestBlogPosts',
            'featuredClients'
        ));
    }

    /**
     * Show the about page.
     */
    public function about(): View
    {
        return view('pages.about');
    }

    /**
     * Show the contact page.
     */
    public function contact(): View
    {
        return view('pages.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        // Track contact form start
        $this->visitorAnalytics->trackJourneyStep(
            'Contact Form Submission',
            'form_submit',
            ['form_type' => 'contact']
        );

        try {
            $validated = $request->validate([
                'name' => 'required|string|max:200',
                'email' => 'required|email|max:255',
                'phone' => 'nullable|string|max:20',
                'company' => 'nullable|string|max:200',
                'service' => 'nullable|string|max:100',
                'subject' => 'nullable|string|max:300',
                'message' => 'required|string|max:5000',
                'attachments' => 'nullable|array|max:15',
                'attachments.*' => 'file|max:25600', // 25MB max per file
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please check your input and try again.',
                    'errors' => $e->errors()
                ], 422);
            }
            throw $e;
        }

        try {
            // Process file attachments if any
            $attachmentData = [];
            if ($request->hasFile('attachments')) {
                foreach ($request->file('attachments') as $file) {
                    try {
                        // Use FileService for documents and ImageService for images
                        $mimeType = $file->getMimeType();

                        if (str_starts_with($mimeType, 'image/')) {
                            // Process image files with ImageService
                            $result = app(ImageService::class)->processUploadedFile($file, [
                                'folder' => 'contact-submissions/' . date('Y/m'),
                                'optimize' => true,
                                'convert_to_webp' => true,
                                'scan_for_viruses' => true,
                            ]);
                        } else {
                            // Process document files with FileService
                            $result = app(FileService::class)->processUploadedFile($file, [
                                'folder' => 'contact-submissions/' . date('Y/m'),
                                'scan_for_viruses' => true,
                                'scan_content' => true,
                            ]);
                        }

                        if ($result['success']) {
                            $attachmentData[] = [
                                'original_name' => $file->getClientOriginalName(),
                                'stored_name' => $result['filename'],
                                'path' => $result['path'],
                                'size' => $file->getSize(),
                                'mime_type' => $mimeType,
                                'scan_results' => $result['scan_results'] ?? null,
                            ];
                        } else {
                            Log::warning('File upload failed for contact submission', [
                                'file' => $file->getClientOriginalName(),
                                'error' => $result['message'],
                                'email' => $validated['email']
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Error processing contact form attachment', [
                            'file' => $file->getClientOriginalName(),
                            'error' => $e->getMessage(),
                            'email' => $validated['email']
                        ]);
                    }
                }
            }

        // Add additional data
        $validated['ip_address'] = $request->ip();
        $validated['user_agent'] = $request->userAgent();
        $validated['referrer'] = $request->header('referer');
        $validated['attachments'] = $attachmentData;

        // Create contact submission
        $contactSubmission = ContactSubmission::create($validated);

        // Track contact form submission in visitor analytics
        $this->visitorAnalytics->trackFormInteraction(
            'contact_form',
            'submit',
            true,
            [
                'submission_id' => $contactSubmission->id,
                'service_interest' => $validated['service'] ?? null,
                'has_attachments' => !empty($attachmentData),
                'attachments_count' => count($attachmentData),
                'company' => $validated['company'] ?? null,
                'subject' => $validated['subject'] ?? null,
            ]
        );

        // Track conversion
        $this->visitorAnalytics->trackConversion(
            'contact_inquiry',
            [
                'submission_id' => $contactSubmission->id,
                'service_interest' => $validated['service'] ?? null,
                'company' => $validated['company'] ?? null,
                'lead_quality' => $this->assessLeadQuality($validated),
            ]
        );

        // Update lead score
        $this->visitorAnalytics->updateLeadScore(
            'contact_form',
            ['service_interest' => $validated['service'] ?? null]
        );

        // Send notification email (implement as needed)
        // Mail::to(config('mail.contact_email'))->send(new ContactFormSubmitted($validated));

        $message = 'Thank you for your message! We will get back to you soon.';
        if (!empty($attachmentData)) {
            $fileCount = count($attachmentData);
            $message .= " Your {$fileCount} file(s) have been securely uploaded and scanned.";
        }

            // Return JSON response for AJAX requests
            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'submission_id' => $contactSubmission->uuid
                ]);
            }

            return redirect()->route('contact')->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Contact form submission error', [
                'error' => $e->getMessage(),
                'email' => $validated['email'] ?? $request->get('email'),
                'trace' => $e->getTraceAsString()
            ]);

            if ($request->expectsJson() || $request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while processing your request. Please try again.'
                ], 500);
            }

            return redirect()->route('contact')
                ->withInput()
                ->with('error', 'An error occurred while processing your request. Please try again.');
        }
    }

    /**
     * Show the services page.
     */
    public function services(): View
    {
        $services = Service::active()
            ->ordered()
            ->get();

        return view('pages.services', compact('services'));
    }

    /**
     * Show a specific service.
     */
    public function service(Service $service): View
    {
        // Get related projects
        $relatedProjects = $service->publishedProjects()
            ->limit(6)
            ->get();

        return view('pages.service', compact('service', 'relatedProjects'));
    }

    /**
     * Show the portfolio/projects page.
     */
    public function portfolio(): View
    {
        $projects = Project::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['client', 'service'])
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('pages.portfolio', compact('projects'));
    }

    /**
     * Show a specific project.
     */
    public function project(string $locale, Project $project): View
    {
        // Get related projects
        $relatedProjects = Project::where('is_published', true)
            ->where('is_deleted', false)
            ->where('id', '!=', $project->id)
            ->limit(3)
            ->get();

        return view('pages.project', compact('project', 'relatedProjects'));
    }

    /**
     * Show the blog page.
     */
    public function blog(): View
    {
        // Track blog page visit
        $this->visitorAnalytics->trackPageVisit('Blog Index', [
            'page_type' => 'blog_index',
            'section' => 'blog'
        ]);

        $posts = \App\Models\BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->with(['category', 'author'])
            ->orderBy('published_at', 'desc')
            ->paginate(10);

        // Track blog engagement
        $this->visitorAnalytics->trackJourneyStep(
            'Blog Browse',
            'content_discovery',
            [
                'posts_count' => $posts->total(),
                'page' => $posts->currentPage()
            ]
        );

        return view('pages.blog.index', compact('posts'));
    }

    /**
     * Show a specific blog post.
     */
    public function blogPost(\App\Models\BlogPost $post): View
    {
        // Track blog post view
        $this->visitorAnalytics->trackPageVisit($post->title, [
            'page_type' => 'blog_post',
            'post_id' => $post->id,
            'post_slug' => $post->slug,
            'category' => $post->category?->name,
            'author' => $post->author->first_name . ' ' . $post->author->last_name,
            'reading_time' => $post->reading_time,
            'word_count' => str_word_count(strip_tags($post->content)),
            'has_gallery' => !empty($post->gallery_images),
            'gallery_count' => count($post->gallery_images ?? []),
            'services' => $post->services()->pluck('name')->toArray()
        ]);

        // Increment view count
        $post->incrementViewCount();

        // Track content engagement
        $this->visitorAnalytics->trackJourneyStep(
            'Blog Post Read',
            'content_consumption',
            [
                'post_id' => $post->id,
                'category' => $post->category?->name,
                'estimated_read_time' => $post->reading_time,
                'engagement_type' => 'blog_read'
            ]
        );

        // Get related posts (same category or related services)
        $relatedPosts = \App\Models\BlogPost::where('is_published', true)
            ->where('is_deleted', false)
            ->where('id', '!=', $post->id)
            ->where(function($query) use ($post) {
                $query->where('category_id', $post->category_id);

                // Also include posts with overlapping services using the model scope
                if (!empty($post->service_ids)) {
                    $query->orWhere(function($subQuery) use ($post) {
                        $subQuery->withOverlappingServices($post);
                    });
                }
            })
            ->orderBy('is_featured', 'desc')
            ->orderBy('published_at', 'desc')
            ->limit(3)
            ->get();

        return view('pages.blog-post', compact('post', 'relatedPosts'));
    }

    /**
     * Assess lead quality from contact form data.
     */
    private function assessLeadQuality(array $data): string
    {
        $score = 0;

        // Company provided
        if (!empty($data['company'])) {
            $score += 20;
        }

        // Phone provided
        if (!empty($data['phone'])) {
            $score += 15;
        }

        // Service interest specified
        if (!empty($data['service'])) {
            $score += 25;
        }

        // Message length indicates engagement
        $messageLength = strlen($data['message'] ?? '');
        if ($messageLength > 200) {
            $score += 20;
        } elseif ($messageLength > 100) {
            $score += 10;
        }

        // Subject indicates specific need
        if (!empty($data['subject'])) {
            $score += 10;
        }

        return match(true) {
            $score >= 70 => 'high',
            $score >= 40 => 'medium',
            default => 'low'
        };
    }
}

@extends('layouts.dashboard')

@section('title', 'Application Submitted Successfully')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-12">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
                <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Application Submitted Successfully!</h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Thank you for your project application. We've received your submission and will review it carefully.
            </p>
        </div>

        <!-- Application Details Card -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                <h2 class="text-xl font-semibold text-white">Application Details</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Reference Number</h3>
                        <p class="text-2xl font-bold text-blue-600 font-mono">{{ $application->reference_number }}</p>
                        <p class="text-sm text-gray-500 mt-1">Keep this number for your records</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Project Title</h3>
                        <p class="text-lg font-semibold text-gray-900">{{ $application->title }}</p>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Status</h3>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                            </svg>
                            {{ ucfirst($application->status) }}
                        </span>
                    </div>
                    <div>
                        <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Submitted</h3>
                        <p class="text-lg text-gray-900">{{ $application->submitted_at->format('M d, Y \a\t g:i A') }}</p>
                    </div>
                </div>

                @if($application->attachments && count($application->attachments) > 0)
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-3">Uploaded Files</h3>
                    <div class="space-y-2">
                        @foreach($application->attachments as $attachment)
                        <div class="flex items-center text-sm text-gray-600">
                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                            {{ $attachment['original_name'] }}
                            <span class="ml-2 text-xs text-gray-400">({{ number_format($attachment['size'] / 1024, 1) }} KB)</span>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Next Steps -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-6 py-4">
                <h2 class="text-xl font-semibold text-white">What Happens Next?</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">1</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Review Process</h3>
                            <p class="text-gray-600">Our team will carefully review your application and requirements within 1-2 business days.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">2</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Initial Contact</h3>
                            <p class="text-gray-600">We'll reach out to discuss your project in detail and answer any questions you may have.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 text-blue-600 font-semibold text-sm">3</div>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-medium text-gray-900">Proposal & Timeline</h3>
                            <p class="text-gray-600">We'll provide a detailed proposal with timeline and cost estimates for your project.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Check -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Check Application Status</h2>
                <p class="text-gray-600 mb-4">You can check the status of your application anytime using your reference number.</p>
                
                <div class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" id="statusReferenceNumber" 
                               placeholder="Enter your reference number" 
                               value="{{ $application->reference_number }}"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <button onclick="checkStatus()" 
                            class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium">
                        Check Status
                    </button>
                </div>
                
                <div id="statusResult" class="mt-4 hidden"></div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="bg-white rounded-xl shadow-lg overflow-hidden">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 mb-4">Need Help?</h2>
                <p class="text-gray-600 mb-4">If you have any questions about your application or our services, don't hesitate to contact us.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Email Us</h3>
                        <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700"><EMAIL></a>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-900 mb-2">Call Us</h3>
                        <a href="tel:+27123456789" class="text-blue-600 hover:text-blue-700">+27 12 345 6789</a>
                    </div>
                </div>
                
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex flex-col sm:flex-row gap-4">
                        <a href="{{ route('home', ['locale' => app()->getLocale()]) }}"
                           class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Back to Home
                        </a>
                        <a href="{{ route('contact', ['locale' => app()->getLocale()]) }}"
                           class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
                            </svg>
                            Contact Us
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function checkStatus() {
    const referenceNumber = document.getElementById('statusReferenceNumber').value.trim();
    const resultDiv = document.getElementById('statusResult');
    const button = document.querySelector('button[onclick="checkStatus()"]');

    if (!referenceNumber) {
        showStatusResult('Please enter a reference number.', 'error');
        return;
    }

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Checking...';
    showStatusResult('Checking status...', 'loading');

    // Track form interaction start
    if (typeof trackFormInteraction === 'function') {
        trackFormInteraction('application_status_check_form', 'submit_start', true, {
            reference_number_length: referenceNumber.length,
            timestamp: new Date().toISOString()
        });
    }

    fetch('{{ route("apply.project.status") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ reference_number: referenceNumber })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            const app = data.application;
            const statusBadgeClass = getStatusBadgeClass(app.status);
            const statusHtml = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <h3 class="font-medium text-green-900">Application Found</h3>
                    </div>
                    <div class="text-sm text-green-700 space-y-2">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="font-medium text-gray-700">Reference Number:</p>
                                <p class="font-mono text-sm">${app.reference_number}</p>
                            </div>
                            <div>
                                <p class="font-medium text-gray-700">Status:</p>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusBadgeClass}">
                                    ${formatStatus(app.status)}
                                </span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-700">Title:</p>
                                <p>${app.title}</p>
                            </div>
                            <div>
                                <p class="font-medium text-gray-700">Submitted:</p>
                                <p>${formatDate(app.submitted_at)}</p>
                            </div>
                            ${app.reviewed_at ? `
                            <div>
                                <p class="font-medium text-gray-700">Last Updated:</p>
                                <p>${formatDate(app.reviewed_at)}</p>
                            </div>
                            ` : ''}
                            ${app.admin_notes ? `
                            <div class="md:col-span-2">
                                <p class="font-medium text-gray-700">Notes:</p>
                                <p class="text-gray-600">${app.admin_notes}</p>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;
            showStatusResult(statusHtml, 'success');

            // Track successful status check
            if (typeof trackFormInteraction === 'function') {
                trackFormInteraction('application_status_check_form', 'submit_success', true, {
                    reference_number: referenceNumber,
                    application_found: true,
                    application_status: app.status,
                    application_id: app.id,
                    timestamp: new Date().toISOString()
                });
            }
        } else {
            showStatusResult(data.message, 'error');

            // Track failed status check
            if (typeof trackFormInteraction === 'function') {
                trackFormInteraction('application_status_check_form', 'submit_failed', false, {
                    reference_number: referenceNumber,
                    application_found: false,
                    error_message: data.message,
                    timestamp: new Date().toISOString()
                });
            }
        }
    })
    .catch(error => {
        console.error('Status check error:', error);
        showStatusResult('An error occurred while checking status. Please try again.', 'error');

        // Track error
        if (typeof trackFormInteraction === 'function') {
            trackFormInteraction('application_status_check_form', 'submit_error', false, {
                reference_number: referenceNumber,
                error_type: 'network_error',
                error_message: error.message,
                timestamp: new Date().toISOString()
            });
        }
    })
    .finally(() => {
        // Reset button state
        button.disabled = false;
        button.innerHTML = 'Check Status';
    });
}

function showStatusResult(content, type) {
    const resultDiv = document.getElementById('statusResult');
    resultDiv.classList.remove('hidden');

    let icon;
    switch (type) {
        case 'error':
            icon = '<svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>';
            resultDiv.innerHTML = `<div class="bg-red-50 border border-red-200 rounded-lg p-4"><div class="flex items-center"><div class="flex-shrink-0">${icon}</div><div class="text-red-700">${content}</div></div></div>`;
            break;
        case 'loading':
            icon = '<svg class="animate-spin w-5 h-5 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>';
            resultDiv.innerHTML = `<div class="bg-blue-50 border border-blue-200 rounded-lg p-4"><div class="flex items-center"><div class="flex-shrink-0">${icon}</div><div class="text-blue-700">${content}</div></div></div>`;
            break;
        default:
            resultDiv.innerHTML = content;
    }
}

// Helper function to get status badge classes
function getStatusBadgeClass(status) {
    const statusClasses = {
        'pending': 'bg-yellow-100 text-yellow-800',
        'under_review': 'bg-blue-100 text-blue-800',
        'approved': 'bg-green-100 text-green-800',
        'rejected': 'bg-red-100 text-red-800',
        'in_progress': 'bg-indigo-100 text-indigo-800',
        'completed': 'bg-green-100 text-green-800',
        'cancelled': 'bg-gray-100 text-gray-800'
    };
    return statusClasses[status] || 'bg-gray-100 text-gray-800';
}

// Helper function to format status text
function formatStatus(status) {
    const statusLabels = {
        'pending': 'Pending',
        'under_review': 'Under Review',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'in_progress': 'In Progress',
        'completed': 'Completed',
        'cancelled': 'Cancelled'
    };
    return statusLabels[status] || status.charAt(0).toUpperCase() + status.slice(1);
}

// Helper function to format dates
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateString;
    }
}

// Global function for visitor analytics tracking
function trackFormInteraction(formId, action, success, data = {}) {
    // This function will be called by the visitor analytics service
    // if it's available on the page
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackFormInteraction) {
        window.visitorAnalytics.trackFormInteraction(formId, action, success, data);
    }
}

// Initialize visitor analytics tracking on page load
document.addEventListener('DOMContentLoaded', function() {
    // Track page visit for application success page
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackPageVisit) {
        window.visitorAnalytics.trackPageVisit('Project Application Success', {
            application_id: {{ $application->id }},
            reference_number: '{{ $application->reference_number }}',
            application_status: '{{ $application->status }}',
            service_id: {{ $application->service_id ?? 'null' }},
            is_authenticated: {{ auth()->check() ? 'true' : 'false' }},
            page_type: 'application_success'
        });
    }

    // Track form view for status check form
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.trackFormInteraction) {
        window.visitorAnalytics.trackFormInteraction('application_status_check_form', 'view', true, {
            form_location: 'success_page',
            pre_filled_reference: '{{ $application->reference_number }}',
            timestamp: new Date().toISOString()
        });
    }

    // Update lead score for successful application submission
    if (typeof window.visitorAnalytics !== 'undefined' && window.visitorAnalytics.updateLeadScore) {
        window.visitorAnalytics.updateLeadScore('project_application_success', {
            application_submitted: true,
            application_id: {{ $application->id }},
            service_type: '{{ $application->service?->name ?? "unknown" }}',
            budget_range: '{{ $application->budget_range ?? "not_specified" }}',
            urgency: '{{ $application->urgency ?? "not_specified" }}'
        });
    }
});

// Enhanced error handling for AJAX requests
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection in status check:', event.reason);

    // Track error in visitor analytics
    if (typeof trackFormInteraction === 'function') {
        trackFormInteraction('application_status_check_form', 'unhandled_error', false, {
            error_type: 'unhandled_promise_rejection',
            error_message: event.reason?.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
});
</script>
@endsection

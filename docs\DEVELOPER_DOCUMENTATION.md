# ChiSolution - Comprehensive Developer Documentation

## 📋 Table of Contents

1. [Project Overview](#project-overview)
2. [Architecture & Design Patterns](#architecture--design-patterns)
3. [Global Services & Code Reusability](#global-services--code-reusability)
4. [Coding Standards & Rules](#coding-standards--rules)
5. [Package Management & Dependencies](#package-management--dependencies)
6. [Frontend Development Guide](#frontend-development-guide)
7. [Backend Development Guide](#backend-development-guide)
8. [Database Architecture](#database-architecture)
9. [Testing Strategy](#testing-strategy)
10. [User Roles & Permissions](#user-roles--permissions)
11. [Security Implementation](#security-implementation)
12. [Performance Optimization](#performance-optimization)
13. [Deployment & DevOps](#deployment--devops)
14. [Getting Started for New Developers](#getting-started-for-new-developers)

---

## 🎯 Project Overview

**ChiSolution** is a comprehensive digital agency platform built with Laravel 12 and modern web technologies. It serves as a multi-purpose business solution featuring:

### Core Business Modules
- **E-commerce Platform**: Complete product catalog, shopping cart, and order management
- **Project Management**: Client project applications and tracking system
- **Career Portal**: Job postings and application management
- **Professional Accounting Services**: Bookkeeping, payroll, tax preparation
- **Live Chat & AI Customer Support**: Real-time messaging with AI integration
- **Content Management**: Blog system with multi-language support
- **Email Marketing**: Campaign management and newsletter system

### Technical Stack
- **Backend**: Laravel 12.19.3, PHP 8.2+
- **Frontend**: Tailwind CSS 4.x (exclusively), Vite, JavaScript ES6+
- **Database**: MySQL with comprehensive migrations
- **Real-time**: Laravel Broadcasting with WebSocket support
- **AI Integration**: OpenAI GPT with circuit breaker protection
- **Caching**: Redis for sessions and performance optimization
- **Payment Processing**: Stripe integration
- **Image Processing**: Intervention Image with Spatie optimization
- **Security**: Laravel Sanctum with custom monitoring
- **Testing**: PHPUnit with 95%+ coverage
- **Containerization**: Docker with production-ready deployment

---

## 🏗️ Architecture & Design Patterns

### Service-Oriented Architecture (SOA)

ChiSolution follows a **Service-Oriented Architecture** with clear separation of concerns:

```
app/
├── Services/           # Business logic layer
├── Models/            # Data layer (Eloquent ORM)
├── Http/Controllers/  # Presentation layer
├── Http/Middleware/   # Request/Response filtering
├── Events/           # Event-driven architecture
├── Listeners/        # Event handlers
├── Jobs/             # Background processing
├── Notifications/    # Communication layer
└── Traits/           # Reusable functionality
```

### Design Patterns Implemented

1. **Repository Pattern**: Implemented through Services
2. **Observer Pattern**: Model observers for automated actions
3. **Factory Pattern**: Model factories for testing and seeding
4. **Facade Pattern**: Custom facades for global services
5. **Strategy Pattern**: AI provider system with multiple providers
6. **Circuit Breaker Pattern**: For external API protection
7. **Singleton Pattern**: Global services registration

### Dependency Injection

All services use Laravel's dependency injection container:

```php
// Service registration in AppServiceProvider
public function register(): void
{
    $this->app->singleton(ImageService::class);
    $this->app->singleton(FileService::class);
    $this->app->singleton(ChatService::class);
    $this->app->singleton(ActivityLogger::class);
}
```

---

## 🔧 Global Services & Code Reusability

### Core Global Services

#### 1. ImageService (`app/Services/ImageService.php`)
**Purpose**: Comprehensive image processing, optimization, and security
**Location**: `app/Services/ImageService.php`
**Configuration**: `config/image.php`
**Facade**: `App\Facades\ImageService`

**Key Features**:
- Image validation and security scanning
- Automatic WebP conversion
- Multiple size variants generation
- EXIF data removal
- Virus scanning integration

**Usage Examples**:
```php
// Using the service directly
$imageService = app(ImageService::class);
$result = $imageService->processUploadedImage($file);

// Using the facade
use App\Facades\ImageService;
$result = ImageService::processUploadedImage($file);

// Using global helpers
$result = process_image($file);
$result = quick_image_upload($file, 'products');
$result = full_image_upload($file, 'gallery');
```

#### 2. FileService (`app/Services/FileService.php`)
**Purpose**: Secure file upload, validation, and processing
**Location**: `app/Services/FileService.php`
**Configuration**: `config/file.php`
**Facade**: `App\Facades\FileService`

**Key Features**:
- Multi-format file support (documents, archives, data files)
- Content analysis and text extraction
- Archive extraction and scanning
- Metadata removal
- Comprehensive security validation

**Usage Examples**:
```php
// Process any file type
$result = file_service()->processUploadedFile($file);

// Quick secure upload
$result = secure_file_upload($file, 'documents');

// Extract text from documents
$textContent = extract_file_text($filePath);
```

#### 3. ChatService (`app/Services/ChatService.php`)
**Purpose**: Real-time chat management with AI integration
**Location**: `app/Services/ChatService.php`
**Configuration**: `config/chat.php`

**Key Features**:
- Real-time messaging with WebSocket support
- AI chatbot integration with OpenAI
- Multi-language support
- Staff assignment and routing
- Analytics and reporting

#### 4. ActivityLogger (`app/Services/ActivityLogger.php`)
**Purpose**: Comprehensive security and activity monitoring
**Location**: `app/Services/ActivityLogger.php`

**Key Features**:
- User activity tracking
- Security event logging
- Risk assessment and scoring
- Suspicious activity detection
- Device fingerprinting

### Reusable Traits

#### 1. Translatable Trait (`app/Traits/Translatable.php`)
**Purpose**: Multi-language content support
**Usage**: Add to any model requiring translations

```php
use App\Traits\Translatable;

class Product extends Model
{
    use Translatable;
    
    protected $translatable = ['name', 'description'];
}
```

### Global Helper Functions (`app/helpers.php`)

The application provides 50+ global helper functions for common operations:

**Image Helpers**:
```php
process_image($file, $options = [])
quick_image_upload($file, $subdirectory = 'uploads')
full_image_upload($file, $subdirectory = 'uploads')
optimize_image($imagePath, $options = [])
convert_to_webp($imagePath, $quality = null)
```

**File Helpers**:
```php
process_file($file, $options = [])
secure_file_upload($file, $subdirectory = '')
validate_file($file)
extract_file_text($filePath)
```

**Localization Helpers**:
```php
formatCurrency($amount, $currency = 'ZAR')
localizedRoute($name, $parameters = [], $locale = null)
currentLocale()
availableLocales()
```

**Performance Helpers**:
```php
performance_optimizer()
safe_htmlspecialchars($value)
safe_string_cast($value)
```

---

## 📏 Coding Standards & Rules

### PHP Coding Standards

1. **PSR-12 Compliance**: All PHP code follows PSR-12 standards
2. **Type Declarations**: Use strict typing where possible
3. **Return Types**: Always declare return types
4. **Nullable Types**: Use nullable types appropriately

```php
<?php

declare(strict_types=1);

namespace App\Services;

class ExampleService
{
    public function processData(array $data): ?array
    {
        // Implementation
        return $result ?? null;
    }
}
```

### Naming Conventions

1. **Classes**: PascalCase (`UserController`, `ImageService`)
2. **Methods**: camelCase (`getUserData`, `processImage`)
3. **Variables**: camelCase (`$userData`, `$imageResult`)
4. **Constants**: UPPER_SNAKE_CASE (`MAX_FILE_SIZE`)
5. **Database Tables**: snake_case (`user_preferences`, `chat_messages`)
6. **Routes**: kebab-case (`/admin/user-preferences`)

### File Organization Rules

1. **Controllers**: Group by functionality in subdirectories
   - `app/Http/Controllers/Admin/` - Admin functionality
   - `app/Http/Controllers/Api/V1/` - API endpoints
   - `app/Http/Controllers/Auth/` - Authentication

2. **Services**: Single responsibility principle
   - One service per business domain
   - Use traits for shared functionality
   - Dependency injection for service dependencies

3. **Models**: Rich domain models with relationships
   - Use UUIDs for public-facing identifiers
   - Implement soft deletes where appropriate
   - Use observers for automated actions

### Documentation Standards

1. **PHPDoc**: All public methods must have PHPDoc comments
2. **Inline Comments**: Explain complex business logic
3. **README Files**: Each major feature should have documentation

```php
/**
 * Process uploaded image with full security pipeline
 * 
 * @param UploadedFile $file The uploaded image file
 * @param array $options Processing options
 * @return array Processing result with success status and file info
 * @throws ImageProcessingException When processing fails
 */
public function processUploadedImage(UploadedFile $file, array $options = []): array
{
    // Implementation
}
```

---

## 📦 Package Management & Dependencies

### Core Laravel Packages

```json
{
    "require": {
        "laravel/framework": "^12.0",
        "laravel/sanctum": "^4.1",
        "laravel/tinker": "^2.10.1"
    }
}
```

### Image & File Processing
```json
{
    "intervention/image-laravel": "^1.5",
    "spatie/image-optimizer": "^1.8",
    "spatie/laravel-sitemap": "^7.3"
}
```

### AI & External Services
```json
{
    "openai-php/client": "^0.14.0",
    "openai-php/laravel": "^0.14.0",
    "stripe/stripe-php": "^17.3"
}
```

### SEO & Analytics
```json
{
    "artesaos/seotools": "^1.3",
    "spatie/schema-org": "^3.23",
    "jenssegers/agent": "*"
}
```

### Development & Testing
```json
{
    "require-dev": {
        "phpunit/phpunit": "^11.5",
        "brianium/paratest": "^7.8",
        "laravel/pint": "^1.13",
        "mockery/mockery": "^1.6"
    }
}
```

### Frontend Dependencies
```json
{
    "devDependencies": {
        "@tailwindcss/forms": "^0.5.10",
        "@tailwindcss/typography": "^0.5.16",
        "tailwindcss": "^4.1.10",
        "vite": "^6.2.4",
        "axios": "^1.8.2"
    }
}
```

### Package Usage Guidelines

1. **Always use package managers**: Never edit package files directly
2. **Version constraints**: Use semantic versioning constraints
3. **Security updates**: Regularly update dependencies
4. **Custom packages**: Create custom packages for reusable functionality

---

## 🎨 Frontend Development Guide

### Tailwind CSS 4.x Architecture

ChiSolution uses **Tailwind CSS 4.x exclusively** for all styling. No other CSS frameworks are used.

#### Configuration (`tailwind.config.js`)
```javascript
export default {
  theme: {
    extend: {
      colors: {
        primary: {
          600: '#1e3a8a', // Dark blue
          700: '#1e40af',
        },
        secondary: {
          600: '#166534', // Forest green
          700: '#14532d',
        }
      }
    }
  }
}
```

#### Custom CSS Architecture (`resources/css/app.css`)

The application follows a structured CSS approach:

1. **CSS Variables**: Defined in `:root` for consistent theming
2. **Component Classes**: Reusable component styles
3. **Utility Extensions**: Custom utilities extending Tailwind
4. **Responsive Design**: Mobile-first approach

```css
/* Custom CSS Variables */
:root {
  --color-primary-600: #1e3a8a;
  --color-secondary-600: #166534;
}

/* Component Classes */
.btn-primary {
  background-color: #2563eb;
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
}

/* Floating Label Form System */
.floating-input-group {
  position: relative;
  margin-bottom: 2rem;
}
```

### Component System

#### 1. Floating Label Forms
**Location**: Global CSS in `app.css`
**Usage**: Consistent form styling across the application

```html
<div class="floating-input-group">
    <input type="text" class="floating-input" placeholder=" " required>
    <label class="floating-label">Full Name</label>
</div>
```

#### 2. Card Components
```html
<div class="card">
    <!-- Content -->
</div>

<div class="card-hover">
    <!-- Content with hover effects -->
</div>
```

#### 3. Button System
```html
<button class="btn-primary">Primary Action</button>
<button class="btn-secondary">Secondary Action</button>
<button class="btn-outline">Outline Button</button>
```

### JavaScript Architecture (`resources/js/`)

#### Structure
```
resources/js/
├── app.js              # Main application entry
├── bootstrap.js        # Laravel/Axios configuration
├── professional-carousel.js  # Carousel component
└── chat-widget.js      # Chat widget (standalone)
```

#### Component Development Pattern
```javascript
class ComponentName {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        if (!this.container) return;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.setupResponsive();
    }
    
    bindEvents() {
        // Event listeners
    }
    
    setupResponsive() {
        // Responsive behavior
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    new ComponentName('component-container');
});
```

### Responsive Design Guidelines

1. **Mobile-First**: Always design for mobile first
2. **Breakpoints**: Use Tailwind's standard breakpoints
   - `sm`: 640px
   - `md`: 768px  
   - `lg`: 1024px
   - `xl`: 1280px
   - `2xl`: 1536px

3. **Container System**: Three container variants
   - `.container`: Standard responsive container
   - `.container-fluid`: Full-width container
   - `.container-wide`: Wide responsive container (95% width)

### Blade Template Structure

#### Layout Hierarchy
```
resources/views/layouts/
├── app.blade.php       # Main application layout
├── auth.blade.php      # Authentication layout
└── dashboard.blade.php # Dashboard layout
```

#### Component Organization
```
resources/views/components/
├── admin/              # Admin-specific components
├── seo.blade.php      # SEO meta tags
├── language-switcher.blade.php
└── carousel-3d.blade.php
```

#### Page Structure
```
resources/views/pages/
├── home.blade.php
├── about.blade.php
├── contact.blade.php
├── shop/              # E-commerce pages
├── blog/              # Blog pages
└── services/          # Service pages

---

## 🔧 Backend Development Guide

### Controller Architecture

#### Controller Organization
```
app/Http/Controllers/
├── Controller.php          # Base controller
├── HomeController.php      # Public pages
├── Admin/                  # Admin functionality
│   ├── DashboardController.php
│   ├── ProductController.php
│   ├── UserController.php
│   └── ChatController.php
├── Api/V1/                # API endpoints
│   ├── ChatController.php
│   └── AuthController.php
└── Auth/                  # Authentication
    ├── LoginController.php
    ├── RegisterController.php
    └── ForgotPasswordController.php
```

#### Controller Best Practices

1. **Single Responsibility**: Each controller handles one resource
2. **Dependency Injection**: Inject services in constructor
3. **Request Validation**: Use Form Requests for validation
4. **Resource Methods**: Follow RESTful conventions

```php
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ProductStoreRequest;
use App\Services\ImageService;
use App\Services\ActivityLogger;
use App\Models\Product;

class ProductController extends Controller
{
    public function __construct(
        private ImageService $imageService,
        private ActivityLogger $activityLogger
    ) {}

    public function store(ProductStoreRequest $request)
    {
        $validated = $request->validated();

        // Process images if uploaded
        if ($request->hasFile('featured_image')) {
            $imageResult = $this->imageService->processUploadedImage(
                $request->file('featured_image')
            );

            if ($imageResult['success']) {
                $validated['featured_image'] = $imageResult['path'];
            }
        }

        $product = Product::create($validated);

        $this->activityLogger->logActivity(
            'product_created',
            'Product created: ' . $product->name,
            $product->toArray()
        );

        return redirect()
            ->route('admin.products.show', $product)
            ->with('success', 'Product created successfully');
    }
}
```

### Service Layer Architecture

#### Service Design Principles

1. **Business Logic Encapsulation**: All business logic in services
2. **Dependency Injection**: Services depend on other services
3. **Interface Segregation**: Use contracts for complex services
4. **Single Responsibility**: One service per business domain

```php
<?php

namespace App\Services;

use App\Models\Order;
use App\Models\Product;
use App\Services\PaymentService;
use App\Services\EmailNotificationService;
use Illuminate\Support\Facades\DB;

class OrderService
{
    public function __construct(
        private PaymentService $paymentService,
        private EmailNotificationService $emailService,
        private ActivityLogger $activityLogger
    ) {}

    public function createOrder(array $orderData): Order
    {
        return DB::transaction(function () use ($orderData) {
            // Create order
            $order = Order::create($orderData);

            // Process payment
            $paymentResult = $this->paymentService->processPayment(
                $order->total,
                $orderData['payment_method']
            );

            if (!$paymentResult['success']) {
                throw new PaymentException($paymentResult['message']);
            }

            // Update inventory
            $this->updateInventory($order->items);

            // Send notifications
            $this->emailService->sendOrderConfirmation($order);

            // Log activity
            $this->activityLogger->logActivity(
                'order_created',
                "Order #{$order->id} created",
                $order->toArray()
            );

            return $order;
        });
    }

    private function updateInventory(array $items): void
    {
        foreach ($items as $item) {
            $product = Product::find($item['product_id']);
            if ($product && $product->track_inventory) {
                $product->decrement('inventory_quantity', $item['quantity']);
            }
        }
    }
}
```

### Model Architecture

#### Model Best Practices

1. **Rich Domain Models**: Include business logic in models
2. **Relationships**: Define all relationships
3. **Scopes**: Use query scopes for common queries
4. **Observers**: Use observers for automated actions
5. **UUIDs**: Use UUIDs for public-facing identifiers

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Product extends Model
{
    protected $fillable = [
        'uuid', 'name', 'slug', 'description', 'price',
        'featured_image', 'gallery', 'is_active', 'is_featured'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'gallery' => 'array',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($product) {
            if (empty($product->uuid)) {
                $product->uuid = Str::uuid();
            }
            if (empty($product->slug)) {
                $product->slug = Str::slug($product->name);
            }
        });
    }

    // Route key binding
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    // Relationships
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(ProductCategory::class, 'product_category_relations');
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class);
    }

    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_deleted', false);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeInCategory($query, $categoryId)
    {
        return $query->whereHas('categories', function ($q) use ($categoryId) {
            $q->where('product_categories.id', $categoryId);
        });
    }

    // Accessors & Mutators
    public function getFeaturedImageUrlAttribute(): string
    {
        if ($this->featured_image) {
            return get_image_url($this->featured_image);
        }
        return asset('images/placeholder-product.jpg');
    }

    public function getFormattedPriceAttribute(): string
    {
        return formatCurrency($this->price);
    }

    // Business Logic Methods
    public function isInStock(): bool
    {
        if (!$this->track_inventory) {
            return true;
        }
        return $this->inventory_quantity > 0;
    }

    public function canPurchase(int $quantity = 1): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->track_inventory) {
            return $this->inventory_quantity >= $quantity;
        }

        return true;
    }
}
```

### Request Validation

#### Form Request Classes

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return $this->user()->can('create', Product::class);
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'slug' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('products')->ignore($this->product)
            ],
            'description' => ['required', 'string'],
            'price' => ['required', 'numeric', 'min:0'],
            'featured_image' => ['nullable', 'image', 'max:10240'], // 10MB
            'gallery.*' => ['nullable', 'image', 'max:10240'],
            'categories' => ['required', 'array', 'min:1'],
            'categories.*' => ['exists:product_categories,id'],
            'track_inventory' => ['boolean'],
            'inventory_quantity' => ['required_if:track_inventory,true', 'integer', 'min:0'],
        ];
    }

    public function messages(): array
    {
        return [
            'categories.required' => 'Please select at least one category.',
            'featured_image.max' => 'Featured image must not exceed 10MB.',
            'inventory_quantity.required_if' => 'Inventory quantity is required when tracking inventory.',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'track_inventory' => $this->boolean('track_inventory'),
            'is_active' => $this->boolean('is_active', true),
            'is_featured' => $this->boolean('is_featured', false),
        ]);
    }
}
```

### Middleware Implementation

#### Custom Middleware Examples

```php
<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\ActivityLogger;

class ActivityLoggingMiddleware
{
    public function __construct(
        private ActivityLogger $activityLogger
    ) {}

    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Log significant activities
        if ($this->shouldLog($request)) {
            $this->activityLogger->logRequest($request, $response);
        }

        return $response;
    }

    private function shouldLog(Request $request): bool
    {
        // Skip logging for static assets and health checks
        $skipPaths = ['/health', '/favicon.ico', '/robots.txt'];

        foreach ($skipPaths as $path) {
            if ($request->is($path)) {
                return false;
            }
        }

        return true;
    }
}
```

### Event-Driven Architecture

#### Events and Listeners

```php
<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderCreated
{
    use Dispatchable, SerializesModels;

    public function __construct(
        public Order $order
    ) {}
}
```

```php
<?php

namespace App\Listeners;

use App\Events\OrderCreated;
use App\Services\EmailNotificationService;
use App\Services\InventoryService;

class ProcessOrderCreated
{
    public function __construct(
        private EmailNotificationService $emailService,
        private InventoryService $inventoryService
    ) {}

    public function handle(OrderCreated $event): void
    {
        $order = $event->order;

        // Send confirmation email
        $this->emailService->sendOrderConfirmation($order);

        // Update inventory
        $this->inventoryService->updateFromOrder($order);

        // Notify admin if low stock
        $this->inventoryService->checkLowStockAlerts($order);
    }
}

---

## 🗄️ Database Architecture

### Migration Strategy

#### Migration Organization
```
database/migrations/
├── 0001_01_01_000000_create_roles_table.php
├── 0001_01_01_000001_create_users_table.php
├── 2025_06_25_153950_create_product_categories_table.php
├── 2025_06_25_154018_create_products_table.php
├── 2025_07_31_061406_create_chat_rooms_table.php
└── 2025_08_01_120000_create_chat_webhooks_table.php
```

#### Migration Best Practices

1. **Descriptive Names**: Use clear, descriptive migration names
2. **Incremental Changes**: Small, focused migrations
3. **Rollback Support**: Always implement `down()` methods
4. **Index Strategy**: Add indexes for performance
5. **Foreign Keys**: Use proper foreign key constraints

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description');
            $table->decimal('price', 10, 2);
            $table->decimal('compare_price', 10, 2)->nullable();
            $table->string('sku')->unique()->nullable();
            $table->string('featured_image')->nullable();
            $table->json('gallery')->nullable();
            $table->boolean('track_inventory')->default(false);
            $table->integer('inventory_quantity')->default(0);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_deleted')->default(false);
            $table->timestamps();

            // Indexes for performance
            $table->index(['is_active', 'is_deleted']);
            $table->index(['is_featured', 'is_active']);
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
```

### Database Relationships

#### Core Entity Relationships

```
Users (1) ←→ (∞) Orders
Users (1) ←→ (∞) ProjectApplications
Users (1) ←→ (∞) JobApplications
Users (1) ←→ (∞) ChatParticipants

Products (∞) ←→ (∞) ProductCategories
Products (1) ←→ (∞) ProductReviews
Products (1) ←→ (∞) OrderItems

Orders (1) ←→ (∞) OrderItems
Orders (1) ←→ (∞) Payments

ChatRooms (1) ←→ (∞) ChatMessages
ChatRooms (1) ←→ (∞) ChatParticipants
ChatRooms (1) ←→ (1) ChatAssignment
```

### Seeding Strategy

#### Database Seeders
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;
use App\Models\User;
use App\Models\ProductCategory;

class DatabaseSeeder extends Seeder
{
    public function run(): void
    {
        // Create roles first
        $this->call([
            RoleSeeder::class,
            UserSeeder::class,
            ProductCategorySeeder::class,
            ProductSeeder::class,
            ChatSystemSettingsSeeder::class,
        ]);
    }
}
```

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Role;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        $roles = [
            [
                'name' => 'admin',
                'permissions' => [
                    'users' => ['create', 'read', 'update', 'delete'],
                    'products' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'chat' => ['moderate', 'assign', 'analytics'],
                    'permissions' => ['manage'],
                ]
            ],
            [
                'name' => 'staff',
                'permissions' => [
                    'products' => ['read', 'update'],
                    'orders' => ['read', 'update'],
                    'chat' => ['participate', 'moderate'],
                ]
            ],
            [
                'name' => 'customer',
                'permissions' => [
                    'profile' => ['read', 'update'],
                    'orders' => ['read'],
                    'chat' => ['participate'],
                ]
            ]
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name']],
                ['permissions' => $roleData['permissions']]
            );
        }
    }
}
```

---

## 🧪 Testing Strategy

### Testing Architecture

#### Test Organization
```
tests/
├── Feature/                # Integration tests
│   ├── Admin/             # Admin functionality tests
│   ├── Auth/              # Authentication tests
│   ├── Services/          # Service integration tests
│   └── Api/               # API endpoint tests
├── Unit/                  # Unit tests
│   ├── Models/            # Model tests
│   ├── Services/          # Service unit tests
│   └── Helpers/           # Helper function tests
└── TestCase.php           # Base test class
```

#### Testing Standards

1. **95%+ Coverage**: Maintain high test coverage for critical features
2. **Test Naming**: Descriptive test method names
3. **AAA Pattern**: Arrange, Act, Assert
4. **Database Transactions**: Use RefreshDatabase trait
5. **Mocking**: Mock external services

### Feature Testing Examples

```php
<?php

namespace Tests\Feature\Services;

use Tests\TestCase;
use App\Services\ImageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ImageServiceFeatureTest extends TestCase
{
    use RefreshDatabase;

    private ImageService $imageService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->imageService = app(ImageService::class);
        Storage::fake('public');
    }

    /** @test */
    public function it_processes_uploaded_image_successfully(): void
    {
        // Arrange
        $file = UploadedFile::fake()->image('test.jpg', 800, 600);

        // Act
        $result = $this->imageService->processUploadedImage($file);

        // Assert
        $this->assertTrue($result['success']);
        $this->assertArrayHasKey('path', $result);
        $this->assertArrayHasKey('variants', $result);

        // Verify file was stored
        Storage::disk('public')->assertExists($result['path']);

        // Verify variants were created
        foreach ($result['variants'] as $variant) {
            Storage::disk('public')->assertExists($variant['path']);
        }
    }

    /** @test */
    public function it_rejects_invalid_file_types(): void
    {
        // Arrange
        $file = UploadedFile::fake()->create('document.pdf', 1000);

        // Act
        $result = $this->imageService->processUploadedImage($file);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertArrayHasKey('errors', $result);
        $this->assertContains('Invalid file type', $result['errors']);
    }

    /** @test */
    public function it_handles_oversized_images(): void
    {
        // Arrange
        $file = UploadedFile::fake()->image('large.jpg', 5000, 5000)->size(15000); // 15MB

        // Act
        $result = $this->imageService->processUploadedImage($file);

        // Assert
        $this->assertFalse($result['success']);
        $this->assertStringContainsString('file size', $result['errors'][0]);
    }
}
```

### Unit Testing Examples

```php
<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\Product;
use App\Models\ProductCategory;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_generates_uuid_on_creation(): void
    {
        // Act
        $product = Product::factory()->create();

        // Assert
        $this->assertNotNull($product->uuid);
        $this->assertIsString($product->uuid);
    }

    /** @test */
    public function it_generates_slug_from_name(): void
    {
        // Arrange
        $productName = 'Test Product Name';

        // Act
        $product = Product::factory()->create(['name' => $productName]);

        // Assert
        $this->assertEquals('test-product-name', $product->slug);
    }

    /** @test */
    public function it_checks_stock_availability_correctly(): void
    {
        // Arrange
        $product = Product::factory()->create([
            'track_inventory' => true,
            'inventory_quantity' => 5
        ]);

        // Act & Assert
        $this->assertTrue($product->isInStock());
        $this->assertTrue($product->canPurchase(3));
        $this->assertFalse($product->canPurchase(10));
    }

    /** @test */
    public function it_returns_formatted_price(): void
    {
        // Arrange
        $product = Product::factory()->create(['price' => 99.99]);

        // Act
        $formattedPrice = $product->formatted_price;

        // Assert
        $this->assertEquals('R 99.99', $formattedPrice);
    }

    /** @test */
    public function it_has_categories_relationship(): void
    {
        // Arrange
        $product = Product::factory()->create();
        $category = ProductCategory::factory()->create();

        // Act
        $product->categories()->attach($category);

        // Assert
        $this->assertTrue($product->categories->contains($category));
    }
}
```

### API Testing

```php
<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\User;
use App\Models\ChatRoom;
use Laravel\Sanctum\Sanctum;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ChatApiTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_chat_room_via_api(): void
    {
        // Arrange
        $payload = [
            'type' => 'visitor',
            'priority' => 2,
            'language' => 'en',
            'visitor_info' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
            ],
        ];

        // Act
        $response = $this->postJson('/api/v1/chat/rooms', $payload);

        // Assert
        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'uuid',
                        'type',
                        'status',
                        'priority',
                        'language'
                    ]
                ]);

        $this->assertDatabaseHas('chat_rooms', [
            'type' => 'visitor',
            'priority' => 2,
            'language' => 'en'
        ]);
    }

    /** @test */
    public function authenticated_user_can_send_message(): void
    {
        // Arrange
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $chatRoom = ChatRoom::factory()->create();

        $payload = [
            'message' => 'Hello, I need help with my order',
            'type' => 'text'
        ];

        // Act
        $response = $this->postJson("/api/v1/chat/rooms/{$chatRoom->uuid}/messages", $payload);

        // Assert
        $response->assertStatus(201);

        $this->assertDatabaseHas('chat_messages', [
            'chat_room_id' => $chatRoom->id,
            'user_id' => $user->id,
            'message' => 'Hello, I need help with my order',
            'type' => 'text'
        ]);
    }
}
```

### Testing Commands

```bash
# Run all tests
php artisan test

# Run specific test suite
php artisan test --testsuite=Feature
php artisan test --testsuite=Unit

# Run with coverage
php artisan test --coverage

# Run specific test file
php artisan test tests/Feature/Services/ImageServiceFeatureTest.php

# Run tests in parallel
php artisan test --parallel

# Run tests with specific filter
php artisan test --filter=ImageService
```

---

## 👥 User Roles & Permissions

### Role-Based Access Control (RBAC)

#### Role Hierarchy
```
Admin (Full Access)
├── Staff (Limited Admin Access)
├── Customer (E-commerce Access)
└── Guest (Public Access)
```

#### Permission Matrix

| Resource | Admin | Staff | Customer | Guest |
|----------|-------|-------|----------|-------|
| Users | CRUD | R | Profile Only | - |
| Products | CRUD | RU | R | R |
| Orders | CRUD | RU | Own Orders | - |
| Chat | Moderate | Participate | Participate | Participate |
| Analytics | Full | Limited | - | - |
| Permissions | Manage | - | - | - |

#### Dashboard Features by Role

### Admin Dashboard Features
**Location**: `/admin/dashboard`
**Access**: `role:admin`

**Core Features**:
- **System Overview**: Total users, orders, revenue, chat sessions
- **Real-time Analytics**: Live visitor tracking, conversion rates
- **User Management**: Create, edit, delete users and roles
- **Product Management**: Full CRUD operations on products and categories
- **Order Management**: Process orders, update statuses, refunds
- **Chat Moderation**: Monitor chat sessions, assign staff, AI configuration
- **Email Marketing**: Campaign management, subscriber analytics
- **Financial Reports**: Revenue tracking, payment analytics
- **Security Monitoring**: Activity logs, suspicious activity alerts
- **System Settings**: Configuration management, feature toggles

**Navigation Structure**:
```
Dashboard
├── Analytics & Reports
│   ├── Visitor Analytics
│   ├── Sales Reports
│   ├── Chat Analytics
│   └── Email Analytics
├── User Management
│   ├── Users
│   ├── Roles & Permissions
│   └── Activity Logs
├── E-commerce
│   ├── Products
│   ├── Categories
│   ├── Orders
│   └── Coupons
├── Content Management
│   ├── Projects
│   ├── Blog Posts
│   ├── Job Postings
│   └── Contact Submissions
├── Communication
│   ├── Live Chat
│   ├── Email Campaigns
│   ├── Newsletter Subscribers
│   └── AI Configuration
└── System
    ├── Settings
    ├── Security
    └── Performance
```

### Staff Dashboard Features
**Location**: `/admin/dashboard`
**Access**: `role:staff`

**Limited Features**:
- **Order Processing**: View and update order statuses
- **Product Updates**: Edit product information (no delete)
- **Chat Support**: Participate in chat sessions, moderate conversations
- **Customer Service**: View customer information, order history
- **Basic Reports**: Sales summaries, chat performance

**Restricted Access**:
- ❌ User creation/deletion
- ❌ System settings
- ❌ Permission management
- ❌ Financial reports
- ❌ Security logs

### Customer Dashboard Features
**Location**: `/dashboard`
**Access**: `role:customer`

**Personal Features**:
- **Order History**: View past orders, track shipments, download invoices
- **Profile Management**: Update personal information, addresses
- **Project Applications**: Submit and track project requests
- **Job Applications**: Apply for positions, track application status
- **Support**: Access live chat, submit support tickets
- **Preferences**: Communication preferences, notification settings

**E-commerce Features**:
- **Wishlist**: Save favorite products
- **Reviews**: Write and manage product reviews
- **Addresses**: Manage shipping and billing addresses
- **Payment Methods**: Saved payment methods (tokenized)

### Permission Implementation

#### Middleware Usage
```php
// Route protection examples
Route::middleware(['permission:users,read'])->group(function () {
    Route::get('/admin/users', [UserController::class, 'index']);
});

Route::middleware(['permission:products,create'])->group(function () {
    Route::post('/admin/products', [ProductController::class, 'store']);
});

Route::middleware(['permission:chat,moderate'])->group(function () {
    Route::post('/admin/chat/assign', [ChatController::class, 'assign']);
});
```

#### Controller Permission Checks
```php
public function store(Request $request)
{
    $this->authorize('create', Product::class);

    // Implementation
}

public function update(Request $request, Product $product)
{
    $this->authorize('update', $product);

    // Implementation
}
```

#### Blade Template Permissions
```blade
@can('create', App\Models\Product::class)
    <a href="{{ route('admin.products.create') }}" class="btn-primary">
        Add Product
    </a>
@endcan

@role('admin')
    <div class="admin-only-content">
        <!-- Admin-specific content -->
    </div>
@endrole

@hasPermission('users', 'delete')
    <button class="btn-danger" onclick="deleteUser({{ $user->id }})">
        Delete User
    </button>
@endhasPermission
```

---

## 🔒 Security Implementation

### Security Layers

#### 1. Authentication Security
- **Laravel Sanctum**: API token authentication
- **Password Hashing**: Bcrypt with configurable rounds
- **Rate Limiting**: Login attempt protection
- **CSRF Protection**: All forms protected
- **Session Security**: Secure session configuration

#### 2. File Upload Security
**ImageService Security Features**:
- File type validation (MIME type + extension)
- File signature verification (magic bytes)
- Virus scanning integration
- EXIF data removal
- Filename sanitization
- Size and dimension limits

**FileService Security Features**:
- Content analysis and pattern detection
- Archive extraction with depth limits
- Metadata removal
- Suspicious content scanning
- Quarantine system for infected files

#### 3. Input Validation & Sanitization
```php
// Request validation
public function rules(): array
{
    return [
        'name' => ['required', 'string', 'max:255', 'regex:/^[a-zA-Z\s]+$/'],
        'email' => ['required', 'email:rfc,dns', 'unique:users'],
        'content' => ['required', 'string', new NoScriptTag],
    ];
}

// Custom validation rule
class NoScriptTag implements Rule
{
    public function passes($attribute, $value): bool
    {
        return !preg_match('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', $value);
    }
}
```

#### 4. Activity Logging & Monitoring
**ActivityLogger Features**:
- User action tracking
- Security event logging
- Risk scoring system
- Suspicious activity detection
- Device fingerprinting
- Geolocation tracking

```php
// Automatic activity logging
$this->activityLogger->logActivity(
    'user_login',
    'User logged in successfully',
    [
        'user_id' => $user->id,
        'ip_address' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'risk_score' => $this->calculateRiskScore($request)
    ]
);
```

#### 5. API Security
- **Rate Limiting**: Per-endpoint rate limits
- **API Versioning**: Versioned endpoints for stability
- **Input Validation**: Strict request validation
- **Response Filtering**: Sensitive data filtering
- **CORS Configuration**: Proper cross-origin settings

#### 6. Database Security
- **Query Protection**: Eloquent ORM prevents SQL injection
- **Mass Assignment Protection**: Fillable/guarded properties
- **Soft Deletes**: Preserve data integrity
- **Encryption**: Sensitive data encryption
- **Backup Strategy**: Regular automated backups

### Security Configuration

#### Environment Security
```env
# Security settings
APP_ENV=production
APP_DEBUG=false
APP_KEY=base64:generated-key

# Session security
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# File upload security
IMAGE_ENABLE_VIRUS_SCAN=true
FILE_VIRUS_SCAN_ENABLED=true
FILE_VALIDATE_CONTENT=true

# Rate limiting
THROTTLE_REQUESTS=60
THROTTLE_DECAY_MINUTES=1
```

#### Security Headers
```php
// SecurityHeadersMiddleware
public function handle($request, Closure $next)
{
    $response = $next($request);

    $response->headers->set('X-Content-Type-Options', 'nosniff');
    $response->headers->set('X-Frame-Options', 'DENY');
    $response->headers->set('X-XSS-Protection', '1; mode=block');
    $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    $response->headers->set('Content-Security-Policy', "default-src 'self'");

    return $response;
}
```

---

## ⚡ Performance Optimization

### Caching Strategy

#### 1. Application Caching
```php
// Service-level caching
class ProductService
{
    public function getFeaturedProducts(): Collection
    {
        return Cache::remember('featured_products', 3600, function () {
            return Product::featured()->active()->with('categories')->get();
        });
    }

    public function clearProductCache(): void
    {
        Cache::forget('featured_products');
        Cache::tags(['products'])->flush();
    }
}
```

#### 2. Database Optimization
- **Query Optimization**: Eager loading relationships
- **Indexing Strategy**: Strategic database indexes
- **Connection Pooling**: Optimized database connections
- **Query Caching**: Cache expensive queries

```php
// Optimized queries with eager loading
$products = Product::with(['categories', 'reviews'])
    ->active()
    ->featured()
    ->limit(10)
    ->get();

// Avoid N+1 queries
$orders = Order::with(['items.product', 'user'])
    ->where('status', 'completed')
    ->get();
```

#### 3. Image Optimization
- **WebP Conversion**: Automatic WebP generation
- **Multiple Sizes**: Responsive image variants
- **Lazy Loading**: Frontend lazy loading implementation
- **CDN Integration**: Asset delivery optimization

#### 4. Frontend Performance
- **Asset Bundling**: Vite for optimized builds
- **CSS Optimization**: Tailwind CSS purging
- **JavaScript Optimization**: Code splitting and minification
- **Caching Headers**: Proper browser caching

### Performance Monitoring

#### PerformanceOptimizer Service
```php
class PerformanceOptimizer
{
    public function optimizeQuery(string $key, callable $callback, int $ttl = 3600)
    {
        return Cache::remember($key, $ttl, $callback);
    }

    public function measureExecutionTime(callable $callback): array
    {
        $start = microtime(true);
        $result = $callback();
        $executionTime = microtime(true) - $start;

        return [
            'result' => $result,
            'execution_time' => $executionTime
        ];
    }
}

---

## 🚀 Deployment & DevOps

### Docker Configuration

#### Production Docker Setup
```dockerfile
# Dockerfile
FROM php:8.2-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    nginx

# Install PHP extensions
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . .

# Install dependencies
RUN composer install --optimize-autoloader --no-dev

# Set permissions
RUN chown -R www-data:www-data /var/www
RUN chmod -R 755 /var/www/storage

EXPOSE 9000
CMD ["php-fpm"]
```

#### Docker Compose for Development
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chisolution-app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    networks:
      - chisolution

  nginx:
    image: nginx:alpine
    container_name: chisolution-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./:/var/www
      - ./deployment/nginx:/etc/nginx/conf.d
    networks:
      - chisolution

  mysql:
    image: mysql:8.0
    container_name: chisolution-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: chisolution
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_PASSWORD: secret
      MYSQL_USER: chisolution
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - chisolution

  redis:
    image: redis:alpine
    container_name: chisolution-redis
    restart: unless-stopped
    networks:
      - chisolution

networks:
  chisolution:
    driver: bridge

volumes:
  mysql_data:
    driver: local
```

### Deployment Scripts

#### Production Deployment
```bash
#!/bin/bash
# deployment/deploy.sh

set -e

echo "🚀 Starting ChiSolution deployment..."

# Pull latest code
git pull origin main

# Install/update dependencies
composer install --optimize-autoloader --no-dev
npm ci
npm run build

# Run database migrations
php artisan migrate --force

# Clear and cache configuration
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize application
php artisan optimize

# Restart services
sudo systemctl restart php8.2-fpm
sudo systemctl restart nginx

# Clear application cache
php artisan cache:clear

echo "✅ Deployment completed successfully!"
```

### Environment Configuration

#### Production Environment
```env
# .env.production
APP_NAME="ChiSolution"
APP_ENV=production
APP_KEY=base64:your-production-key
APP_DEBUG=false
APP_URL=https://chisolution.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=chisolution_prod
DB_USERNAME=chisolution
DB_PASSWORD=secure-password

# Cache & Sessions
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=redis-password
REDIS_PORT=6379

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your-username
MAIL_PASSWORD=your-password

# File Storage
FILESYSTEM_DISK=s3
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=chisolution-assets

# Security
IMAGE_ENABLE_VIRUS_SCAN=true
FILE_VIRUS_SCAN_ENABLED=true
ACTIVITY_LOGGING_ENABLED=true
```

---

## 🎯 Getting Started for New Developers

### Quick Setup Guide

#### 1. Prerequisites
- **PHP 8.2+** with extensions: mbstring, xml, ctype, json, bcmath, gd
- **Composer** for PHP dependency management
- **Node.js 18+** and npm for frontend assets
- **MySQL 8.0+** for database
- **Redis** for caching and sessions (optional but recommended)

#### 2. Installation Steps

```bash
# Clone the repository
git clone <repository-url>
cd chisolution

# Install PHP dependencies
composer install

# Install Node.js dependencies
npm install

# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Configure database in .env file
# DB_DATABASE=chisolution
# DB_USERNAME=your-username
# DB_PASSWORD=your-password

# Run database migrations
php artisan migrate

# Seed the database
php artisan db:seed

# Build frontend assets
npm run dev

# Start development server
composer run dev
```

#### 3. Development Workflow

**Daily Development Commands**:
```bash
# Start all development services
composer run dev

# Run tests
php artisan test

# Code formatting
./vendor/bin/pint

# Clear caches during development
php artisan optimize:clear
```

### Project Structure Overview

```
chisolution/
├── app/                    # Application code
│   ├── Http/Controllers/   # Request handlers
│   ├── Models/            # Database models
│   ├── Services/          # Business logic
│   ├── Middleware/        # Request/response filters
│   └── helpers.php        # Global helper functions
├── config/                # Configuration files
├── database/              # Migrations, seeders, factories
├── docs/                  # Documentation
├── resources/             # Frontend assets and views
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript
│   └── views/            # Blade templates
├── routes/               # Route definitions
├── storage/              # File storage and logs
├── tests/                # Test suites
└── public/               # Web server document root
```

### Key Learning Resources

#### 1. Essential Documentation
- **Laravel 12 Documentation**: https://laravel.com/docs/12.x
- **Tailwind CSS 4.x**: https://tailwindcss.com/docs
- **PHPUnit Testing**: https://phpunit.de/documentation.html

#### 2. Project-Specific Docs
- **API Documentation**: `docs/API_DOCUMENTATION.md`
- **Chat System**: `docs/CHAT_API_DOCUMENTATION.md`
- **Image Service**: `docs/ImageService.md`
- **User Permissions**: `docs/USER-PERMISSION-SYSTEM.md`

#### 3. Code Examples
- **Service Usage**: Check `app/helpers.php` for global functions
- **Controller Patterns**: Review `app/Http/Controllers/Admin/`
- **Model Relationships**: Examine `app/Models/Product.php`
- **Testing Examples**: Browse `tests/Feature/` and `tests/Unit/`

### Development Best Practices

#### 1. Before Starting Development
- Read this documentation thoroughly
- Understand the service architecture
- Review existing code patterns
- Set up your development environment properly

#### 2. When Adding New Features
- Follow the established patterns
- Use existing services where possible
- Write tests for new functionality
- Update documentation as needed

#### 3. Code Review Checklist
- [ ] Follows PSR-12 coding standards
- [ ] Uses dependency injection
- [ ] Includes proper error handling
- [ ] Has appropriate test coverage
- [ ] Updates relevant documentation
- [ ] Uses existing global services
- [ ] Follows security best practices

### Common Development Tasks

#### Adding a New Model
```bash
# Create model with migration and factory
php artisan make:model ProductVariant -mf

# Create controller
php artisan make:controller Admin/ProductVariantController --resource

# Create form request
php artisan make:request ProductVariantStoreRequest
```

#### Adding a New Service
```php
// 1. Create service class
php artisan make:class Services/NewService

// 2. Register in AppServiceProvider
public function register(): void
{
    $this->app->singleton(NewService::class);
}

// 3. Create facade (optional)
php artisan make:class Facades/NewService
```

#### Adding New Tests
```bash
# Create feature test
php artisan make:test NewFeatureTest

# Create unit test
php artisan make:test NewServiceTest --unit
```

### Troubleshooting Common Issues

#### 1. Permission Errors
```bash
# Fix storage permissions
sudo chown -R www-data:www-data storage/
sudo chmod -R 755 storage/
```

#### 2. Cache Issues
```bash
# Clear all caches
php artisan optimize:clear
php artisan config:clear
php artisan cache:clear
```

#### 3. Database Issues
```bash
# Reset database
php artisan migrate:fresh --seed
```

#### 4. Asset Issues
```bash
# Rebuild assets
npm run build
php artisan optimize
```

---

## 📞 Support & Resources

### Getting Help

1. **Documentation**: Start with this comprehensive guide
2. **Code Examples**: Review existing implementations
3. **Test Cases**: Check test files for usage examples
4. **Laravel Documentation**: Official Laravel docs for framework features

### Contributing Guidelines

1. **Follow Coding Standards**: PSR-12 compliance required
2. **Write Tests**: Maintain 95%+ test coverage
3. **Update Documentation**: Keep docs current with changes
4. **Use Existing Patterns**: Follow established architectural patterns
5. **Security First**: Always consider security implications

### Project Contacts

- **Technical Lead**: Review code architecture decisions
- **DevOps Team**: Deployment and infrastructure questions
- **QA Team**: Testing standards and procedures

---

**Last Updated**: December 2024
**Version**: 1.0
**Maintainer**: ChiSolution Development Team
```
```
```

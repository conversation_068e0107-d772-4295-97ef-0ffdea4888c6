<?php

namespace App\Services;

use App\Models\VisitorAnalytic;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class VisitorAnalyticsDashboard
{
    private const CACHE_TTL = 300; // 5 minutes

    /**
     * Get visitor analytics stats for admin dashboard.
     */
    public function getVisitorStats(): array
    {
        return Cache::remember('visitor_analytics.stats', self::CACHE_TTL, function () {
            $today = today();
            $yesterday = today()->subDay();
            $thisWeek = now()->startOfWeek();
            $lastWeek = now()->subWeek()->startOfWeek();
            $thisMonth = now()->startOfMonth();
            $lastMonth = now()->subMonth()->startOfMonth();

            return [
                // Today's stats
                'today_visitors' => VisitorAnalytic::whereDate('visited_at', $today)->distinct('visitor_id')->count(),
                'today_page_views' => VisitorAnalytic::whereDate('visited_at', $today)->count(),
                'today_returning' => VisitorAnalytic::whereDate('visited_at', $today)->where('is_returning_visitor', true)->distinct('visitor_id')->count(),
                'today_new' => VisitorAnalytic::whereDate('visited_at', $today)->where('is_returning_visitor', false)->distinct('visitor_id')->count(),

                // Yesterday comparison
                'yesterday_visitors' => VisitorAnalytic::whereDate('visited_at', $yesterday)->distinct('visitor_id')->count(),
                'yesterday_page_views' => VisitorAnalytic::whereDate('visited_at', $yesterday)->count(),

                // Weekly stats
                'week_visitors' => VisitorAnalytic::where('visited_at', '>=', $thisWeek)->distinct('visitor_id')->count(),
                'week_page_views' => VisitorAnalytic::where('visited_at', '>=', $thisWeek)->count(),
                'last_week_visitors' => VisitorAnalytic::whereBetween('visited_at', [$lastWeek, $thisWeek])->distinct('visitor_id')->count(),

                // Monthly stats
                'month_visitors' => VisitorAnalytic::where('visited_at', '>=', $thisMonth)->distinct('visitor_id')->count(),
                'month_page_views' => VisitorAnalytic::where('visited_at', '>=', $thisMonth)->count(),
                'last_month_visitors' => VisitorAnalytic::whereBetween('visited_at', [$lastMonth, $thisMonth])->distinct('visitor_id')->count(),

                // Device breakdown
                'device_breakdown' => VisitorAnalytic::selectRaw('device_type, COUNT(DISTINCT visitor_id) as count')
                    ->whereDate('visited_at', '>=', $today->subDays(7))
                    ->groupBy('device_type')
                    ->pluck('count', 'device_type')
                    ->toArray(),

                // Top countries
                'top_countries' => VisitorAnalytic::selectRaw('country, COUNT(DISTINCT visitor_id) as count')
                    ->whereDate('visited_at', '>=', $today->subDays(7))
                    ->whereNotNull('country')
                    ->groupBy('country')
                    ->orderBy('count', 'desc')
                    ->limit(5)
                    ->pluck('count', 'country')
                    ->toArray(),

                // Error stats
                'errors_today' => VisitorAnalytic::whereDate('visited_at', $today)->where('has_errors', true)->count(),
                'bot_visits_today' => VisitorAnalytic::whereDate('visited_at', $today)->where('is_bot', true)->count(),
                'suspicious_visits_today' => VisitorAnalytic::whereDate('visited_at', $today)->where('is_suspicious', true)->count(),
            ];
        });
    }

    /**
     * Get visitor analytics chart data for the last X days.
     */
    public function getVisitorChartData(int $days = 30): array
    {
        return Cache::remember("visitor_analytics.chart.{$days}days", self::CACHE_TTL, function () use ($days) {
            $startDate = now()->subDays($days)->startOfDay();

            $data = VisitorAnalytic::selectRaw('
                DATE(visited_at) as date,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                COUNT(*) as page_views,
                COUNT(DISTINCT CASE WHEN is_returning_visitor = 1 THEN visitor_id END) as returning_visitors,
                COUNT(DISTINCT CASE WHEN is_returning_visitor = 0 THEN visitor_id END) as new_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "mobile" THEN visitor_id END) as mobile_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "desktop" THEN visitor_id END) as desktop_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "tablet" THEN visitor_id END) as tablet_visitors,
                COUNT(DISTINCT CASE WHEN is_bounce = 1 THEN visitor_id END) as bounce_visitors,
                COUNT(DISTINCT CASE WHEN form_submitted = 1 THEN visitor_id END) as form_submissions,
                COUNT(DISTINCT CASE WHEN converted = 1 THEN visitor_id END) as conversions,
                AVG(session_duration) as avg_session_duration,
                AVG(scroll_depth) as avg_scroll_depth,
                COUNT(DISTINCT CASE WHEN is_suspicious = 1 THEN visitor_id END) as suspicious_visitors,
                COUNT(DISTINCT CASE WHEN country IS NOT NULL THEN visitor_id END) as visitors_with_location
            ')
            ->where('visited_at', '>=', $startDate)
            ->where('is_bot', false)
            ->groupBy(DB::raw('DATE(visited_at)'))
            ->orderBy('date')
            ->get();

            // Fill missing dates with zeros
            $chartData = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                $dayData = $data->firstWhere('date', $date);

                $chartData[] = [
                    'date' => $date,
                    'unique_visitors' => $dayData->unique_visitors ?? 0,
                    'page_views' => $dayData->page_views ?? 0,
                    'returning_visitors' => $dayData->returning_visitors ?? 0,
                    'new_visitors' => $dayData->new_visitors ?? 0,
                    'mobile_visitors' => $dayData->mobile_visitors ?? 0,
                    'desktop_visitors' => $dayData->desktop_visitors ?? 0,
                    'tablet_visitors' => $dayData->tablet_visitors ?? 0,
                    'bounce_visitors' => $dayData->bounce_visitors ?? 0,
                    'form_submissions' => $dayData->form_submissions ?? 0,
                    'conversions' => $dayData->conversions ?? 0,
                    'avg_session_duration' => round($dayData->avg_session_duration ?? 0, 2),
                    'avg_scroll_depth' => round($dayData->avg_scroll_depth ?? 0, 2),
                    'suspicious_visitors' => $dayData->suspicious_visitors ?? 0,
                    'visitors_with_location' => $dayData->visitors_with_location ?? 0,
                ];
            }

            return $chartData;
        });
    }

    /**
     * Get visitor analytics chart data for a custom date range.
     */
    public function getVisitorChartDataByDateRange(string $startDate, string $endDate): array
    {
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();
        $cacheKey = "visitor_analytics.chart.{$start->format('Y-m-d')}.{$end->format('Y-m-d')}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($start, $end) {
            $data = VisitorAnalytic::selectRaw('
                DATE(visited_at) as date,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                COUNT(*) as page_views,
                COUNT(DISTINCT CASE WHEN is_returning_visitor = 1 THEN visitor_id END) as returning_visitors,
                COUNT(DISTINCT CASE WHEN is_returning_visitor = 0 THEN visitor_id END) as new_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "mobile" THEN visitor_id END) as mobile_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "desktop" THEN visitor_id END) as desktop_visitors,
                COUNT(DISTINCT CASE WHEN device_type = "tablet" THEN visitor_id END) as tablet_visitors,
                COUNT(DISTINCT CASE WHEN is_bounce = 1 THEN visitor_id END) as bounce_visitors,
                COUNT(DISTINCT CASE WHEN form_submitted = 1 THEN visitor_id END) as form_submissions,
                COUNT(DISTINCT CASE WHEN converted = 1 THEN visitor_id END) as conversions,
                AVG(session_duration) as avg_session_duration,
                AVG(scroll_depth) as avg_scroll_depth,
                COUNT(DISTINCT CASE WHEN is_suspicious = 1 THEN visitor_id END) as suspicious_visitors,
                COUNT(DISTINCT CASE WHEN country IS NOT NULL THEN visitor_id END) as visitors_with_location
            ')
            ->whereBetween('visited_at', [$start, $end])
            ->where('is_bot', false)
            ->groupBy(DB::raw('DATE(visited_at)'))
            ->orderBy('date')
            ->get();

            // Fill missing dates with zeros
            $chartData = [];
            $currentDate = $start->copy();

            while ($currentDate->lte($end)) {
                $dateString = $currentDate->format('Y-m-d');
                $dayData = $data->firstWhere('date', $dateString);

                $chartData[] = [
                    'date' => $dateString,
                    'unique_visitors' => $dayData->unique_visitors ?? 0,
                    'page_views' => $dayData->page_views ?? 0,
                    'returning_visitors' => $dayData->returning_visitors ?? 0,
                    'new_visitors' => $dayData->new_visitors ?? 0,
                    'mobile_visitors' => $dayData->mobile_visitors ?? 0,
                    'desktop_visitors' => $dayData->desktop_visitors ?? 0,
                    'tablet_visitors' => $dayData->tablet_visitors ?? 0,
                    'bounce_visitors' => $dayData->bounce_visitors ?? 0,
                    'form_submissions' => $dayData->form_submissions ?? 0,
                    'conversions' => $dayData->conversions ?? 0,
                    'avg_session_duration' => round($dayData->avg_session_duration ?? 0, 2),
                    'avg_scroll_depth' => round($dayData->avg_scroll_depth ?? 0, 2),
                    'suspicious_visitors' => $dayData->suspicious_visitors ?? 0,
                    'visitors_with_location' => $dayData->visitors_with_location ?? 0,
                ];

                $currentDate->addDay();
            }

            return $chartData;
        });
    }

    /**
     * Get device breakdown analytics.
     */
    public function getDeviceBreakdown(int $days = 30): array
    {
        return Cache::remember("visitor_analytics.device_breakdown.{$days}days", self::CACHE_TTL, function () use ($days) {
            $startDate = now()->subDays($days)->startOfDay();

            return VisitorAnalytic::selectRaw('
                device_type,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                COUNT(*) as page_views,
                AVG(session_duration) as avg_session_duration
            ')
            ->where('visited_at', '>=', $startDate)
            ->where('is_bot', false)
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->orderBy('unique_visitors', 'desc')
            ->get()
            ->toArray();
        });
    }

    /**
     * Get location analytics.
     */
    public function getLocationAnalytics(int $days = 30, int $limit = 10): array
    {
        return Cache::remember("visitor_analytics.locations.{$days}days.{$limit}", self::CACHE_TTL, function () use ($days, $limit) {
            $startDate = now()->subDays($days)->startOfDay();

            return VisitorAnalytic::selectRaw('
                country,
                city,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                COUNT(*) as page_views,
                AVG(session_duration) as avg_session_duration
            ')
            ->where('visited_at', '>=', $startDate)
            ->where('is_bot', false)
            ->whereNotNull('country')
            ->groupBy('country', 'city')
            ->orderBy('unique_visitors', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'location' => $item->city ? "{$item->city}, {$item->country}" : $item->country,
                    'country' => $item->country,
                    'city' => $item->city,
                    'unique_visitors' => $item->unique_visitors,
                    'page_views' => $item->page_views,
                    'avg_session_duration' => round($item->avg_session_duration ?? 0, 2),
                ];
            })
            ->toArray();
        });
    }

    /**
     * Get top countries chart data for the last X days.
     */
    public function getTopCountriesChartData(int $days = 30, int $limit = 5): array
    {
        return Cache::remember("visitor_analytics.top_countries_chart.{$days}days.{$limit}", self::CACHE_TTL, function () use ($days, $limit) {
            $startDate = now()->subDays($days)->startOfDay();

            $topCountries = VisitorAnalytic::selectRaw('
                country,
                COUNT(DISTINCT visitor_id) as unique_visitors
            ')
            ->where('visited_at', '>=', $startDate)
            ->where('is_bot', false)
            ->whereNotNull('country')
            ->groupBy('country')
            ->orderBy('unique_visitors', 'desc')
            ->limit($limit)
            ->pluck('country')
            ->toArray();

            // Get daily data for each top country
            $chartData = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = now()->subDays($i)->format('Y-m-d');
                $dayData = ['date' => $date];

                foreach ($topCountries as $country) {
                    $visitors = VisitorAnalytic::where('visited_at', '>=', now()->subDays($i)->startOfDay())
                        ->where('visited_at', '<', now()->subDays($i)->endOfDay())
                        ->where('country', $country)
                        ->where('is_bot', false)
                        ->distinct('visitor_id')
                        ->count();

                    $dayData[strtolower(str_replace(' ', '_', $country)) . '_visitors'] = $visitors;
                }

                $chartData[] = $dayData;
            }

            return [
                'chart_data' => $chartData,
                'countries' => $topCountries
            ];
        });
    }

    /**
     * Get top pages by visits.
     */
    public function getTopPages(int $limit = 10, int $days = 7): array
    {
        return Cache::remember("visitor_analytics.top_pages.{$limit}.{$days}days", self::CACHE_TTL, function () use ($limit, $days) {
            return VisitorAnalytic::selectRaw('
                route_name,
                page_title,
                COUNT(*) as visits,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                AVG(session_duration) as avg_duration
            ')
            ->whereDate('visited_at', '>=', today()->subDays($days))
            ->where('is_bot', false)
            ->whereNotNull('route_name')
            ->groupBy('route_name', 'page_title')
            ->orderBy('visits', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
        });
    }

    /**
     * Get top pages by visits for a custom date range.
     */
    public function getTopPagesByDateRange(string $startDate, string $endDate, int $limit = 10): array
    {
        $start = Carbon::parse($startDate)->startOfDay();
        $end = Carbon::parse($endDate)->endOfDay();
        $cacheKey = "visitor_analytics.top_pages.{$limit}.{$start->format('Y-m-d')}.{$end->format('Y-m-d')}";

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($start, $end, $limit) {
            return VisitorAnalytic::selectRaw('
                route_name,
                page_title,
                COUNT(*) as visits,
                COUNT(DISTINCT visitor_id) as unique_visitors,
                AVG(session_duration) as avg_duration
            ')
            ->whereBetween('visited_at', [$start, $end])
            ->where('is_bot', false)
            ->whereNotNull('route_name')
            ->groupBy('route_name', 'page_title')
            ->orderBy('visits', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
        });
    }

    /**
     * Get recent visitors.
     */
    public function getRecentVisitors(int $limit = 20): array
    {
        return Cache::remember("visitor_analytics.recent_visitors.{$limit}", self::CACHE_TTL, function () use ($limit) {
            return VisitorAnalytic::select([
                'id',
                'visitor_id',
                'ip_address',
                'country',
                'region',
                'city',
                'device_type',
                'browser',
                'platform',
                'page_title',
                'route_name',
                'is_returning_visitor',
                'is_bot',
                'is_suspicious',
                'visited_at'
            ])
            ->where('is_bot', false)
            ->orderBy('visited_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($visitor) {
                return [
                    'id' => $visitor->id,
                    'visitor_id' => substr($visitor->visitor_id, 0, 8) . '...',
                    'ip_address' => $visitor->ip_address,
                    'location' => $visitor->location,
                    'device_info' => $visitor->device_info,
                    'page' => $visitor->page_title ?: $visitor->route_name,
                    'is_returning' => $visitor->is_returning_visitor,
                    'is_suspicious' => $visitor->is_suspicious,
                    'visited_at' => $visitor->visited_at->format('M j, Y H:i'),
                    'time_ago' => $visitor->visited_at->diffForHumans(),
                ];
            })
            ->toArray();
        });
    }

    /**
     * Get visitor analytics for a specific time period.
     */
    public function getAnalyticsForPeriod(Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "visitor_analytics.period.{$startDate->format('Y-m-d')}.{$endDate->format('Y-m-d')}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($startDate, $endDate) {
            $query = VisitorAnalytic::whereBetween('visited_at', [$startDate, $endDate]);

            return [
                'total_visitors' => $query->distinct('visitor_id')->count(),
                'total_page_views' => $query->count(),
                'returning_visitors' => $query->where('is_returning_visitor', true)->distinct('visitor_id')->count(),
                'new_visitors' => $query->where('is_returning_visitor', false)->distinct('visitor_id')->count(),
                'bounce_rate' => $this->calculateBounceRate($startDate, $endDate),
                'avg_session_duration' => $query->whereNotNull('session_duration')->avg('session_duration'),
                'top_referrers' => $this->getTopReferrers($startDate, $endDate),
                'browser_breakdown' => $this->getBrowserBreakdown($startDate, $endDate),
                'hourly_distribution' => $this->getHourlyDistribution($startDate, $endDate),
            ];
        });
    }

    /**
     * Calculate bounce rate for a period.
     */
    protected function calculateBounceRate(Carbon $startDate, Carbon $endDate): float
    {
        $totalSessions = VisitorAnalytic::whereBetween('visited_at', [$startDate, $endDate])
            ->distinct('session_id')
            ->count();

        if ($totalSessions === 0) {
            return 0;
        }

        $bounceSessions = VisitorAnalytic::whereBetween('visited_at', [$startDate, $endDate])
            ->where('is_bounce', true)
            ->distinct('session_id')
            ->count();

        return round(($bounceSessions / $totalSessions) * 100, 2);
    }

    /**
     * Get top referrers for a period.
     */
    protected function getTopReferrers(Carbon $startDate, Carbon $endDate, int $limit = 5): array
    {
        return VisitorAnalytic::selectRaw('referrer_url, COUNT(*) as count')
            ->whereBetween('visited_at', [$startDate, $endDate])
            ->whereNotNull('referrer_url')
            ->where('referrer_url', '!=', '')
            ->groupBy('referrer_url')
            ->orderBy('count', 'desc')
            ->limit($limit)
            ->pluck('count', 'referrer_url')
            ->toArray();
    }

    /**
     * Get browser breakdown for a period.
     */
    protected function getBrowserBreakdown(Carbon $startDate, Carbon $endDate): array
    {
        return VisitorAnalytic::selectRaw('browser, COUNT(DISTINCT visitor_id) as count')
            ->whereBetween('visited_at', [$startDate, $endDate])
            ->whereNotNull('browser')
            ->groupBy('browser')
            ->orderBy('count', 'desc')
            ->pluck('count', 'browser')
            ->toArray();
    }

    /**
     * Get hourly visit distribution.
     */
    protected function getHourlyDistribution(Carbon $startDate, Carbon $endDate): array
    {
        return VisitorAnalytic::selectRaw('HOUR(visited_at) as hour, COUNT(*) as count')
            ->whereBetween('visited_at', [$startDate, $endDate])
            ->groupBy(DB::raw('HOUR(visited_at)'))
            ->orderBy('hour')
            ->pluck('count', 'hour')
            ->toArray();
    }

    /**
     * Get conversion statistics.
     */
    public function getConversionStats(): array
    {
        return Cache::remember('visitor_analytics.conversion_stats', self::CACHE_TTL, function () {
            $totalVisitors = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))->count();
            $convertedVisitors = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('converted', true)
                ->count();

            $conversionsByType = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('converted', true)
                ->select('conversion_type', DB::raw('count(*) as count'))
                ->groupBy('conversion_type')
                ->get()
                ->pluck('count', 'conversion_type')
                ->toArray();

            return [
                'total_visitors' => $totalVisitors,
                'converted_visitors' => $convertedVisitors,
                'conversion_rate' => $totalVisitors > 0 ? round(($convertedVisitors / $totalVisitors) * 100, 2) : 0,
                'conversions_by_type' => $conversionsByType,
                'top_conversion_type' => collect($conversionsByType)->keys()->first(),
            ];
        });
    }

    /**
     * Get lead quality statistics.
     */
    public function getLeadQualityStats(): array
    {
        return Cache::remember('visitor_analytics.lead_quality_stats', self::CACHE_TTL, function () {
            $leadsByStatus = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->select('lead_status', DB::raw('count(*) as count'))
                ->groupBy('lead_status')
                ->get()
                ->pluck('count', 'lead_status')
                ->toArray();

            $averageLeadScore = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->avg('lead_score');

            $highValueLeads = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('lead_score', '>=', 70)
                ->count();

            return [
                'leads_by_status' => $leadsByStatus,
                'average_lead_score' => round($averageLeadScore, 1),
                'high_value_leads' => $highValueLeads,
                'total_leads' => array_sum($leadsByStatus),
            ];
        });
    }

    /**
     * Get journey analytics.
     */
    public function getJourneyAnalytics(): array
    {
        return Cache::remember('visitor_analytics.journey_analytics', self::CACHE_TTL, function () {
            $averageJourneyLength = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->avg('journey_length');

            $commonJourneySteps = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->whereNotNull('user_journey')
                ->get()
                ->flatMap(function ($visitor) {
                    return collect($visitor->user_journey)->pluck('step');
                })
                ->countBy()
                ->sortDesc()
                ->take(10)
                ->toArray();

            $exitIntentRate = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('exit_intent', true)
                ->count();

            $totalVisitors = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))->count();

            return [
                'average_journey_length' => round($averageJourneyLength, 1),
                'common_journey_steps' => $commonJourneySteps,
                'exit_intent_rate' => $totalVisitors > 0 ? round(($exitIntentRate / $totalVisitors) * 100, 2) : 0,
                'visitors_with_exit_intent' => $exitIntentRate,
            ];
        });
    }

    /**
     * Get conversion funnel data.
     */
    public function getConversionFunnel(): array
    {
        return Cache::remember('visitor_analytics.conversion_funnel', self::CACHE_TTL, function () {
            $totalVisitors = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))->count();

            $formViews = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->whereJsonContains('user_journey', ['type' => 'form_view'])
                ->count();

            $formSubmissions = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('form_submitted', true)
                ->count();

            $conversions = VisitorAnalytic::where('visited_at', '>=', now()->subDays(30))
                ->where('converted', true)
                ->count();

            return [
                'visitors' => $totalVisitors,
                'form_views' => $formViews,
                'form_submissions' => $formSubmissions,
                'conversions' => $conversions,
                'view_to_submit_rate' => $formViews > 0 ? round(($formSubmissions / $formViews) * 100, 2) : 0,
                'submit_to_convert_rate' => $formSubmissions > 0 ? round(($conversions / $formSubmissions) * 100, 2) : 0,
                'overall_conversion_rate' => $totalVisitors > 0 ? round(($conversions / $totalVisitors) * 100, 2) : 0,
            ];
        });
    }

    /**
     * Get high-value leads.
     */
    public function getHighValueLeads(int $limit = 10): array
    {
        return Cache::remember("visitor_analytics.high_value_leads.{$limit}", self::CACHE_TTL, function () use ($limit) {
            return VisitorAnalytic::select([
                'id',
                'visitor_id',
                'page_title',
                'country',
                'region',
                'city',
                'device_type',
                'browser',
                'platform',
                'lead_score',
                'visited_at'
            ])
                ->where('visited_at', '>=', now()->subDays(30))
                ->where('lead_score', '>=', 60)
                ->orderBy('lead_score', 'desc')
                ->orderBy('visited_at', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($visitor) {
                    return [
                        'id' => $visitor->id,
                        'visitor_id' => substr($visitor->visitor_id, 0, 8) . '...',
                        'page_title' => $visitor->page_title,
                        'location' => $visitor->location,
                        'device_info' => $visitor->device_info,
                        'visited_at' => $visitor->visited_at->format('M j, Y H:i'),
                        'time_since' => $visitor->visited_at->diffForHumans(),
                        'lead_score' => $visitor->lead_score,
                        'lead_status' => $visitor->lead_status,
                        'converted' => $visitor->converted,
                        'conversion_type' => $visitor->conversion_type,
                        'journey_summary' => $visitor->journey_summary,
                        'conversion_summary' => $visitor->conversion_summary,
                    ];
                })
                ->toArray();
        });
    }

    /**
     * Clear visitor analytics cache.
     */
    public function clearCache(): void
    {
        $patterns = [
            'visitor_analytics.stats',
            'visitor_analytics.chart.*',
            'visitor_analytics.top_pages.*',
            'visitor_analytics.recent_visitors.*',
            'visitor_analytics.period.*',
            'visitor_analytics.conversion_stats',
            'visitor_analytics.lead_quality_stats',
            'visitor_analytics.journey_analytics',
            'visitor_analytics.conversion_funnel',
            'visitor_analytics.high_value_leads.*',
        ];

        foreach ($patterns as $pattern) {
            if (str_contains($pattern, '*')) {
                // For cache stores that support pattern deletion
                Cache::flush(); // Fallback to full flush
                break;
            } else {
                Cache::forget($pattern);
            }
        }
    }
}

import './bootstrap';
import './professional-carousel';
// import './chat-widget'; // Disabled - using standalone version

// Featured Projects Carousel Component
class FeaturedProjectsCarousel {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        if (!this.container) return;

        this.track = this.container.querySelector('.carousel-track');
        this.slides = this.container.querySelectorAll('.carousel-slide');
        this.prevBtn = this.container.querySelector('.carousel-prev');
        this.nextBtn = this.container.querySelector('.carousel-next');
        this.indicators = this.container.querySelectorAll('.carousel-indicator');

        this.currentSlide = 0;
        this.totalSlides = this.slides.length;
        this.slidesToShow = this.getSlidesToShow();
        this.maxSlide = Math.max(0, this.totalSlides - this.slidesToShow);

        this.init();
    }

    getSlidesToShow() {
        const width = window.innerWidth;
        if (width >= 1024) return 3; // lg: 3 slides
        if (width >= 768) return 2;  // md: 2 slides
        return 1; // sm: 1 slide
    }

    init() {
        if (this.totalSlides === 0) return;

        this.updateCarousel();
        this.bindEvents();
        this.startAutoPlay();

        // Handle window resize
        window.addEventListener('resize', () => {
            this.slidesToShow = this.getSlidesToShow();
            this.maxSlide = Math.max(0, this.totalSlides - this.slidesToShow);
            this.currentSlide = Math.min(this.currentSlide, this.maxSlide);
            this.updateCarousel();
        });
    }

    updateCarousel() {
        if (!this.track) return;

        const slideWidth = 100 / this.slidesToShow;
        const translateX = -(this.currentSlide * slideWidth);

        this.track.style.transform = `translateX(${translateX}%)`;

        // Update navigation buttons
        if (this.prevBtn) {
            this.prevBtn.disabled = this.currentSlide === 0;
            this.prevBtn.classList.toggle('opacity-50', this.currentSlide === 0);
        }

        if (this.nextBtn) {
            this.nextBtn.disabled = this.currentSlide >= this.maxSlide;
            this.nextBtn.classList.toggle('opacity-50', this.currentSlide >= this.maxSlide);
        }

        // Update indicators
        this.indicators.forEach((indicator, index) => {
            const isActive = index === this.currentSlide;
            indicator.classList.toggle('bg-blue-600', isActive);
            indicator.classList.toggle('bg-gray-300', !isActive);
        });
    }

    nextSlide() {
        if (this.currentSlide < this.maxSlide) {
            this.currentSlide++;
            this.updateCarousel();
        }
    }

    prevSlide() {
        if (this.currentSlide > 0) {
            this.currentSlide--;
            this.updateCarousel();
        }
    }

    goToSlide(index) {
        this.currentSlide = Math.max(0, Math.min(index, this.maxSlide));
        this.updateCarousel();
    }

    bindEvents() {
        if (this.prevBtn) {
            this.prevBtn.addEventListener('click', () => this.prevSlide());
        }

        if (this.nextBtn) {
            this.nextBtn.addEventListener('click', () => this.nextSlide());
        }

        this.indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });

        // Pause autoplay on hover
        this.container.addEventListener('mouseenter', () => this.stopAutoPlay());
        this.container.addEventListener('mouseleave', () => this.startAutoPlay());
    }

    startAutoPlay() {
        this.stopAutoPlay();
        if (this.totalSlides > this.slidesToShow) {
            this.autoPlayInterval = setInterval(() => {
                if (this.currentSlide >= this.maxSlide) {
                    this.currentSlide = 0;
                } else {
                    this.currentSlide++;
                }
                this.updateCarousel();
            }, 5000);
        }
    }

    stopAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }
}

// Initialize carousel when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new FeaturedProjectsCarousel('featured-projects-carousel');
});

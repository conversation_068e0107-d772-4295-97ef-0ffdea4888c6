<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'admin',
                'slug' => 'admin',
                'description' => 'Full system access, user management, all CRUD operations',
                'permissions' => json_encode([
                    'users' => ['create', 'read', 'update', 'delete'],
                    'products' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'projects' => ['create', 'read', 'update', 'delete'],
                    'content' => ['create', 'read', 'update', 'delete'],
                    'analytics' => ['read'],
                    'settings' => ['create', 'read', 'update', 'delete'],
                    'team' => ['create', 'read', 'update', 'delete'],
                    'newsletter' => ['create', 'read', 'update', 'delete', 'manage'],
                    'contact_submissions' => ['create', 'read', 'update', 'delete', 'manage'],
                    'chat' => ['participate', 'moderate', 'assign', 'view_all', 'export', 'admin'],
                    'categories' => ['create', 'read', 'update', 'delete'],
                    'coupons' => ['create', 'read', 'update', 'delete'],
                    'jobs' => ['create', 'read', 'update', 'delete'],
                    'job_applications' => ['create', 'read', 'update', 'delete'],
                    'project_applications' => ['create', 'read', 'update', 'delete'],
                    'visitor_analytics' => ['read', 'export'],
                    'activity_logs' => ['read', 'delete'],
                    'permissions' => ['read', 'manage'], // Full permission management access
                    'profile' => ['read', 'update']
                ]),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'staff',
                'slug' => 'staff',
                'description' => 'Limited admin access based on assigned permissions',
                'permissions' => json_encode([
                    'products' => ['create', 'read', 'update'],
                    'orders' => ['create', 'read', 'update'],
                    'projects' => ['create', 'read', 'update'],
                    'content' => ['create', 'read', 'update'],
                    'analytics' => ['read'],
                    'chat' => ['participate', 'moderate'], // Staff can participate and moderate chat
                    'categories' => ['create', 'read', 'update'],
                    'newsletter' => ['read', 'manage'],
                    'contact_submissions' => ['read', 'manage'],
                    'profile' => ['read', 'update']
                    // Note: No 'permissions' => ['manage'] - staff cannot manage permissions by default
                ]),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'client',
                'slug' => 'client',
                'description' => 'Project owners, can view own projects, can shop',
                'permissions' => json_encode([
                    'projects' => ['read'], // own projects only
                    'orders' => ['create', 'read'], // own orders only
                    'chat' => ['participate'], // Clients can participate in chat
                    'profile' => ['read', 'update']
                ]),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'customer',
                'slug' => 'customer',
                'description' => 'Product buyers, order history, profile management',
                'permissions' => json_encode([
                    'orders' => ['create', 'read'], // own orders only
                    'profile' => ['read', 'update'],
                    'products' => ['read'],
                    'chat' => ['participate'] // Customers can participate in chat
                ]),
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];

        DB::table('roles')->insert($roles);
    }
}

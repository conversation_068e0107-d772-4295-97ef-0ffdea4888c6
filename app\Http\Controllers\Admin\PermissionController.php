<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Role;
use App\Services\ActivityLogger;
use App\Events\PermissionChanged;
use App\Events\RolePermissionsChanged;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;

class PermissionController extends Controller
{
    protected ActivityLogger $activityLogger;

    /**
     * Create a new controller instance.
     */
    public function __construct(ActivityLogger $activityLogger)
    {
        $this->middleware('auth');
        $this->middleware('role:admin'); // Only admins can access permission management
        $this->activityLogger = $activityLogger;

        // Additional permission check for permission management
        $this->middleware(function ($request, $next) {
            $user = $request->user();

            // Check if user has permission to manage permissions
            if (!$user->hasPermission('permissions', 'manage')) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You do not have permission to manage user permissions.'
                    ], 403);
                }

                abort(403, 'Access denied. Permission management access required.');
            }

            return $next($request);
        });
    }

    /**
     * Display permission management dashboard.
     */
    public function index(Request $request): View
    {
        $users = User::with('role')
            ->where('is_deleted', false)
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            })
            ->when($request->filled('role'), function ($query) use ($request) {
                $query->whereHas('role', function ($q) use ($request) {
                    $q->where('name', $request->role);
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $roles = Role::active()->get();
        $permissionMatrix = $this->getPermissionMatrix();

        return view('admin.permissions.index', compact('users', 'roles', 'permissionMatrix'));
    }

    /**
     * Show role management interface.
     */
    public function roles(): View
    {
        $roles = Role::withCount('users')->get();
        $availablePermissions = $this->getAvailablePermissionsArray();

        return view('admin.permissions.roles', compact('roles', 'availablePermissions'));
    }

    /**
     * Show user permission details.
     */
    public function show(User $user): View
    {
        $user->load('role');
        $availablePermissions = $this->getAvailablePermissionsArray();
        $userPermissions = $user->role ? $user->role->permissions : [];

        return view('admin.permissions.show', compact('user', 'availablePermissions', 'userPermissions'));
    }

    /**
     * Update user role.
     */
    public function updateUserRole(Request $request, User $user): JsonResponse
    {
        $request->validate([
            'role_id' => ['required', 'exists:roles,id']
        ]);

        $oldRole = $user->role;
        $newRole = Role::find($request->role_id);

        // Prevent admin from removing their own admin role
        if ($user->id === auth()->id() && $oldRole && $oldRole->name === 'admin' && $newRole->name !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'You cannot remove your own admin role.'
            ], 403);
        }

        DB::transaction(function () use ($user, $newRole, $oldRole) {
            $user->update(['role_id' => $newRole->id]);

            // Fire permission changed event
            event(new PermissionChanged(
                $user,
                $oldRole,
                $newRole,
                auth()->user(),
                'role_change',
                ['ip_address' => request()->ip()]
            ));
        });

        return response()->json([
            'success' => true,
            'message' => 'User role updated successfully.',
            'user' => [
                'id' => $user->id,
                'role' => $newRole->name,
                'permissions' => $newRole->permissions
            ]
        ]);
    }

    /**
     * Update role permissions.
     */
    public function updateRolePermissions(Request $request, Role $role): JsonResponse
    {
        $request->validate([
            'permissions' => ['required', 'array'],
            'permissions.*' => ['array'],
            'permissions.*.*' => ['string', 'in:create,read,update,delete,manage']
        ]);

        $oldPermissions = $role->permissions;
        $newPermissions = $request->permissions;

        // Validate permissions structure
        $availablePermissions = $this->getAvailablePermissionsArray();
        foreach ($newPermissions as $resource => $actions) {
            if (!isset($availablePermissions[$resource])) {
                return response()->json([
                    'success' => false,
                    'message' => "Invalid permission resource: {$resource}"
                ], 422);
            }

            foreach ($actions as $action) {
                if (!in_array($action, $availablePermissions[$resource])) {
                    return response()->json([
                        'success' => false,
                        'message' => "Invalid action '{$action}' for resource '{$resource}'"
                    ], 422);
                }
            }
        }

        DB::transaction(function () use ($role, $newPermissions, $oldPermissions) {
            $role->update(['permissions' => $newPermissions]);

            // Fire role permissions changed event
            event(new RolePermissionsChanged(
                $role,
                $oldPermissions,
                $newPermissions,
                auth()->user(),
                ['ip_address' => request()->ip()]
            ));
        });

        return response()->json([
            'success' => true,
            'message' => 'Role permissions updated successfully.',
            'role' => [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => $newPermissions
            ]
        ]);
    }

    /**
     * Bulk update user roles.
     */
    public function bulkUpdateRoles(Request $request): JsonResponse
    {
        $request->validate([
            'user_ids' => ['required', 'array'],
            'user_ids.*' => ['exists:users,id'],
            'role_id' => ['required', 'exists:roles,id']
        ]);

        $userIds = $request->user_ids;
        $roleId = $request->role_id;
        $role = Role::find($roleId);

        // Prevent admin from removing their own admin role
        if (in_array(auth()->id(), $userIds) && auth()->user()->role->name === 'admin' && $role->name !== 'admin') {
            return response()->json([
                'success' => false,
                'message' => 'You cannot remove your own admin role.'
            ], 403);
        }

        $updatedCount = 0;

        DB::transaction(function () use ($userIds, $roleId, $role, &$updatedCount) {
            $users = User::whereIn('id', $userIds)->get();

            foreach ($users as $user) {
                $oldRole = $user->role;
                $user->update(['role_id' => $roleId]);
                $updatedCount++;

                // Fire permission changed event for each user
                event(new PermissionChanged(
                    $user,
                    $oldRole,
                    $role,
                    auth()->user(),
                    'bulk_role_change',
                    [
                        'ip_address' => request()->ip(),
                        'operation_type' => 'bulk',
                        'total_users' => count($userIds)
                    ]
                ));
            }
        });

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updatedCount} users to role '{$role->name}'.",
            'updated_count' => $updatedCount
        ]);
    }

    /**
     * Get permission matrix for display.
     */
    private function getPermissionMatrix(): array
    {
        $roles = Role::all();
        $permissions = $this->getAvailablePermissionsArray();
        $matrix = [];

        foreach ($roles as $role) {
            $matrix[$role->name] = [];
            foreach ($permissions as $resource => $actions) {
                $matrix[$role->name][$resource] = [];
                foreach ($actions as $action) {
                    $matrix[$role->name][$resource][$action] = $role->hasPermission($resource, $action);
                }
            }
        }

        return $matrix;
    }

    /**
     * Get permission matrix API endpoint.
     */
    public function getPermissionMatrixApi(): JsonResponse
    {
        $matrix = $this->getPermissionMatrix();

        return response()->json([
            'success' => true,
            'data' => $matrix
        ]);
    }

    /**
     * Get available permissions API endpoint.
     */
    public function getAvailablePermissionsApi(): JsonResponse
    {
        $permissions = $this->getAvailablePermissionsArray();

        return response()->json([
            'success' => true,
            'data' => $permissions
        ]);
    }

    /**
     * Get available permissions structure.
     */
    private function getAvailablePermissionsArray(): array
    {
        return [
            'users' => ['create', 'read', 'update', 'delete'],
            'products' => ['create', 'read', 'update', 'delete'],
            'orders' => ['create', 'read', 'update', 'delete'],
            'projects' => ['create', 'read', 'update', 'delete'],
            'content' => ['create', 'read', 'update', 'delete'],
            'analytics' => ['read'],
            'settings' => ['create', 'read', 'update', 'delete'],
            'team' => ['create', 'read', 'update', 'delete'],
            'newsletter' => ['create', 'read', 'update', 'delete', 'manage'],
            'contact_submissions' => ['create', 'read', 'update', 'delete', 'manage'],
            'chat' => ['participate', 'moderate', 'assign', 'view_all', 'export', 'admin'],
            'categories' => ['create', 'read', 'update', 'delete'],
            'coupons' => ['create', 'read', 'update', 'delete'],
            'jobs' => ['create', 'read', 'update', 'delete'],
            'job_applications' => ['create', 'read', 'update', 'delete'],
            'project_applications' => ['create', 'read', 'update', 'delete'],
            'visitor_analytics' => ['read', 'export'],
            'activity_logs' => ['read', 'delete'],
            'permissions' => ['read', 'manage'], // Permission to manage permissions
            'profile' => ['read', 'update']
        ];
    }
}
